# 读取CSV文件
file_path = '宁216H5-3实时数据.csv'  # 替换为你的文件路径
df = pd.read_csv(file_path)

# 将'date'列设置为时间列，并转换为适当的时间格式（如果需要）
df['date'] = pd.to_datetime(df['date'])

# 将DEP列转换为浮点数，以防数据类型不正确
df['DEP'] = pd.to_numeric(df['DEP'], errors='coerce')

# 特征列
features = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']

# 设置子图布局
num_features = len(features)
fig, axes = plt.subplots(num_features, 1, figsize=(10, 2 * num_features), sharex=True)

# 使用matplotlib的颜色映射
colors = plt.get_cmap('tab10')

# 绘制每个特征的时间变化曲线
for i, feature in enumerate(features):
    axes[i].plot(df['date'], df[feature], label=feature, color=colors(i % 10))  # 通过索引选择颜色
    axes[i].set_ylabel(feature)
    axes[i].legend(loc='upper right')
    axes[i].grid(True)

    # 禁用科学计数法
    axes[i].yaxis.set_major_formatter(ScalarFormatter(useOffset=False))
    axes[i].ticklabel_format(style='plain', axis='y')  # 禁用科学计数法

# 设置共享的X轴标签
axes[-1].set_xlabel('Time')

# 设置图表标题
fig.suptitle('Feature Trends Over Time', y=1.02)

# 调整布局
plt.tight_layout()
plt.show()
