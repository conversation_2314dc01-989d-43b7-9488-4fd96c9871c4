#!/usr/bin/env python3
"""
数据验证测试模块
验证真实数据的格式、完整性和质量
"""

import os
import sys
import pandas as pd
import numpy as np
import unittest
from pathlib import Path
from datetime import datetime
import warnings

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestDataValidation(unittest.TestCase):
    """数据验证测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.project_root = Path(__file__).parent.parent.parent
        self.anomaly_data_path = self.project_root / "异常检测/Time-Series-Library-main-loss/Time-Series-Library-main/dataset/test"
        self.earlysignal_normal_path = self.project_root / "前驱信号检测/dataset/normal"
        self.earlysignal_precursor_path = self.project_root / "前驱信号检测/dataset/earlysignal2"
        
        self.required_columns = ['date', 'DEP', 'BITDEP', 'HOKHEI', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']
        self.validation_results = {}
    
    def test_anomaly_data_existence(self):
        """测试异常检测数据存在性"""
        if not self.anomaly_data_path.exists():
            self.skipTest(f"异常检测数据路径不存在: {self.anomaly_data_path}")
        
        npy_files = list(self.anomaly_data_path.glob("*实时数据.npy"))
        label_files = list(self.anomaly_data_path.glob("*_label.npy"))
        
        self.assertGreater(len(npy_files), 0, "未找到异常检测数据文件")
        self.assertGreater(len(label_files), 0, "未找到异常检测标签文件")
        
        print(f"✓ 异常检测数据文件: {len(npy_files)} 个数据文件, {len(label_files)} 个标签文件")
        
        # 验证数据文件格式
        sample_data = np.load(npy_files[0])
        sample_label = np.load(label_files[0])
        
        self.assertEqual(len(sample_data.shape), 2, "数据应该是2维数组")
        self.assertEqual(sample_data.shape[1], 11, "特征维度应该是11")
        self.assertEqual(len(sample_label.shape), 1, "标签应该是1维数组")
        self.assertEqual(sample_data.shape[0], sample_label.shape[0], "数据和标签长度应该一致")
        
        print(f"✓ 数据格式验证通过: 数据维度 {sample_data.shape}, 标签维度 {sample_label.shape}")
    
    def test_earlysignal_data_existence(self):
        """测试前驱信号数据存在性"""
        if not self.earlysignal_normal_path.exists():
            self.skipTest(f"前驱信号正常数据路径不存在: {self.earlysignal_normal_path}")
        
        if not self.earlysignal_precursor_path.exists():
            self.skipTest(f"前驱信号前驱数据路径不存在: {self.earlysignal_precursor_path}")
        
        normal_files = list(self.earlysignal_normal_path.glob("*.csv"))
        precursor_files = list(self.earlysignal_precursor_path.glob("*.csv"))
        
        self.assertGreater(len(normal_files), 0, "未找到正常数据文件")
        self.assertGreater(len(precursor_files), 0, "未找到前驱信号数据文件")
        
        print(f"✓ 前驱信号数据文件: {len(normal_files)} 个正常文件, {len(precursor_files)} 个前驱文件")
    
    def test_csv_data_format(self):
        """测试CSV数据格式"""
        if not self.earlysignal_normal_path.exists():
            self.skipTest("前驱信号数据路径不存在")
        
        csv_files = list(self.earlysignal_normal_path.glob("*.csv"))[:5]  # 测试前5个文件
        
        format_issues = []
        
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file)
                
                # 检查列名
                missing_cols = set(self.required_columns) - set(df.columns)
                if missing_cols:
                    format_issues.append(f"{csv_file.name}: 缺少列 {missing_cols}")
                    continue
                
                # 检查数据类型
                if df['date'].dtype == 'object':
                    try:
                        pd.to_datetime(df['date'])
                    except:
                        format_issues.append(f"{csv_file.name}: 时间格式错误")
                
                # 检查数值列
                numeric_cols = ['DEP', 'BITDEP', 'HOKHEI', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']
                for col in numeric_cols:
                    if col in df.columns and not pd.api.types.is_numeric_dtype(df[col]):
                        format_issues.append(f"{csv_file.name}: 列 {col} 不是数值类型")
                
                # 检查数据长度
                if len(df) < 10:
                    format_issues.append(f"{csv_file.name}: 数据长度过短 ({len(df)} 行)")
                
            except Exception as e:
                format_issues.append(f"{csv_file.name}: 读取失败 - {str(e)}")
        
        if format_issues:
            print("⚠ 发现数据格式问题:")
            for issue in format_issues[:10]:  # 只显示前10个问题
                print(f"  - {issue}")
        else:
            print("✓ CSV数据格式验证通过")
        
        # 记录验证结果
        self.validation_results['csv_format_issues'] = len(format_issues)
    
    def test_filename_pattern(self):
        """测试文件名格式"""
        if not self.earlysignal_normal_path.exists():
            self.skipTest("前驱信号数据路径不存在")
        
        csv_files = list(self.earlysignal_normal_path.glob("*.csv"))[:10]
        
        pattern_issues = []
        valid_patterns = 0
        
        for csv_file in csv_files:
            filename = csv_file.name
            
            # 检查是否包含井号和时间信息
            if '井_' in filename and '_to_' in filename:
                valid_patterns += 1
            else:
                pattern_issues.append(filename)
        
        if pattern_issues:
            print(f"⚠ 发现 {len(pattern_issues)} 个文件名格式问题")
            for issue in pattern_issues[:5]:
                print(f"  - {issue}")
        
        print(f"✓ 文件名格式验证: {valid_patterns}/{len(csv_files)} 个文件格式正确")
        
        self.validation_results['filename_pattern_issues'] = len(pattern_issues)
    
    def test_data_quality(self):
        """测试数据质量"""
        if not self.earlysignal_normal_path.exists():
            self.skipTest("前驱信号数据路径不存在")
        
        csv_files = list(self.earlysignal_normal_path.glob("*.csv"))[:5]
        
        quality_issues = []
        
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file)
                
                # 检查缺失值
                missing_ratio = df.isnull().sum().sum() / (len(df) * len(df.columns))
                if missing_ratio > 0.1:
                    quality_issues.append(f"{csv_file.name}: 缺失值比例过高 ({missing_ratio:.2%})")
                
                # 检查数值范围合理性
                numeric_cols = ['DEP', 'BITDEP', 'WOB', 'RPM', 'TOR', 'SPP']
                for col in numeric_cols:
                    if col in df.columns:
                        col_data = df[col].dropna()
                        if len(col_data) > 0:
                            # 检查异常值（使用IQR方法）
                            Q1 = col_data.quantile(0.25)
                            Q3 = col_data.quantile(0.75)
                            IQR = Q3 - Q1
                            outliers = col_data[(col_data < Q1 - 1.5*IQR) | (col_data > Q3 + 1.5*IQR)]
                            
                            if len(outliers) / len(col_data) > 0.2:
                                quality_issues.append(f"{csv_file.name}: 列 {col} 异常值过多 ({len(outliers)}/{len(col_data)})")
                
            except Exception as e:
                quality_issues.append(f"{csv_file.name}: 质量检查失败 - {str(e)}")
        
        if quality_issues:
            print("⚠ 发现数据质量问题:")
            for issue in quality_issues[:10]:
                print(f"  - {issue}")
        else:
            print("✓ 数据质量验证通过")
        
        self.validation_results['data_quality_issues'] = len(quality_issues)
    
    def test_time_continuity(self):
        """测试时间连续性"""
        if not self.earlysignal_normal_path.exists():
            self.skipTest("前驱信号数据路径不存在")
        
        csv_files = list(self.earlysignal_normal_path.glob("*.csv"))[:3]
        
        continuity_issues = []
        
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file)
                
                if 'date' not in df.columns:
                    continue
                
                # 转换时间格式
                df['date'] = pd.to_datetime(df['date'])
                df = df.sort_values('date')
                
                # 计算时间间隔
                time_diffs = df['date'].diff().dropna()
                
                if len(time_diffs) > 0:
                    # 检查时间间隔的一致性
                    median_diff = time_diffs.median()
                    irregular_diffs = time_diffs[abs(time_diffs - median_diff) > pd.Timedelta(seconds=5)]
                    
                    if len(irregular_diffs) / len(time_diffs) > 0.1:
                        continuity_issues.append(f"{csv_file.name}: 时间间隔不规律")
                
            except Exception as e:
                continuity_issues.append(f"{csv_file.name}: 时间连续性检查失败 - {str(e)}")
        
        if continuity_issues:
            print("⚠ 发现时间连续性问题:")
            for issue in continuity_issues:
                print(f"  - {issue}")
        else:
            print("✓ 时间连续性验证通过")
        
        self.validation_results['time_continuity_issues'] = len(continuity_issues)
    
    def test_generate_validation_report(self):
        """生成验证报告"""
        report_path = Path(__file__).parent / "data_validation_report.txt"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("数据验证报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("验证结果汇总:\n")
            f.write("-" * 30 + "\n")
            
            total_issues = sum(self.validation_results.values())
            f.write(f"总问题数: {total_issues}\n")
            
            for test_name, issue_count in self.validation_results.items():
                status = "✓ 通过" if issue_count == 0 else f"⚠ {issue_count} 个问题"
                f.write(f"{test_name}: {status}\n")
            
            f.write("\n建议:\n")
            f.write("-" * 30 + "\n")
            
            if total_issues == 0:
                f.write("数据质量良好，可以进行算法测试。\n")
            else:
                f.write("建议在进行算法测试前解决以下问题:\n")
                if self.validation_results.get('csv_format_issues', 0) > 0:
                    f.write("- 修复CSV文件格式问题\n")
                if self.validation_results.get('filename_pattern_issues', 0) > 0:
                    f.write("- 统一文件命名格式\n")
                if self.validation_results.get('data_quality_issues', 0) > 0:
                    f.write("- 清理数据质量问题\n")
                if self.validation_results.get('time_continuity_issues', 0) > 0:
                    f.write("- 检查时间序列连续性\n")
        
        print(f"✓ 验证报告已生成: {report_path}")


if __name__ == '__main__':
    print("=" * 60)
    print("数据验证测试")
    print("=" * 60)
    
    # 运行测试
    unittest.main(verbosity=2, exit=False)
    
    print("\n" + "=" * 60)
    print("数据验证完成")
    print("=" * 60)
