#!/usr/bin/env python3
"""
使用真实数据训练融合算法
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 添加layers模块路径
layers_path = project_root.parent / "异常检测/Time-Series-Library-main-loss/Time-Series-Library-main"
if layers_path.exists():
    sys.path.insert(0, str(layers_path))

def main():
    """主训练函数"""
    parser = argparse.ArgumentParser(description='融合算法真实数据训练')
    
    # 训练模式选择
    parser.add_argument('--mode', type=str, default='both', choices=['anomaly', 'earlysignal', 'both'],
                        help='训练模式: anomaly(仅异常检测), earlysignal(仅前驱信号), both(两者都训练)')
    
    # 数据路径配置
    parser.add_argument('--anomaly_data_path', type=str, 
                        default='../异常检测/Time-Series-Library-main-loss/Time-Series-Library-main/dataset/',
                        help='异常检测数据路径')
    parser.add_argument('--earlysignal_data_path', type=str, 
                        default='../前驱信号检测/dataset/',
                        help='前驱信号检测数据路径')
    
    # 模型配置
    parser.add_argument('--seq_len', type=int, default=96, help='输入序列长度')
    parser.add_argument('--d_model', type=int, default=128, help='模型维度')
    parser.add_argument('--n_heads', type=int, default=8, help='注意力头数')
    parser.add_argument('--e_layers', type=int, default=3, help='编码器层数')
    parser.add_argument('--d_ff', type=int, default=256, help='前馈网络维度')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout率')
    
    # 训练配置
    parser.add_argument('--batch_size', type=int, default=16, help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='学习率')
    parser.add_argument('--train_epochs', type=int, default=50, help='训练轮数')
    parser.add_argument('--patience', type=int, default=10, help='早停耐心值')
    
    # GPU配置
    parser.add_argument('--use_gpu', action='store_true', default=True, help='使用GPU')
    parser.add_argument('--gpu', type=int, default=0, help='GPU设备号')
    
    # 输出配置
    parser.add_argument('--checkpoints', type=str, default='./checkpoints/', help='模型保存路径')
    parser.add_argument('--results', type=str, default='./results/', help='结果保存路径')
    
    args = parser.parse_args()
    
    # 打印配置
    print("=" * 80)
    print("融合算法真实数据训练")
    print("=" * 80)
    print("训练配置:")
    for key, value in vars(args).items():
        print(f"  {key}: {value}")
    print("=" * 80)
    
    # 检查数据路径
    anomaly_path = Path(args.anomaly_data_path)
    earlysignal_path = Path(args.earlysignal_data_path)
    
    if args.mode in ['anomaly', 'both'] and not anomaly_path.exists():
        print(f"错误: 异常检测数据路径不存在: {anomaly_path}")
        return
    
    if args.mode in ['earlysignal', 'both'] and not earlysignal_path.exists():
        print(f"错误: 前驱信号数据路径不存在: {earlysignal_path}")
        return
    
    # 创建输出目录
    os.makedirs(args.checkpoints, exist_ok=True)
    os.makedirs(args.results, exist_ok=True)
    
    try:
        # 导入必要模块
        from configs.fusion_config import FusionConfig
        from exp.exp_fusion import Exp_Fusion
        
        # 创建配置
        config = FusionConfig()
        
        # 更新配置
        config.seq_len = args.seq_len
        config.d_model = args.d_model
        config.n_heads = args.n_heads
        config.e_layers = args.e_layers
        config.d_ff = args.d_ff
        config.dropout = args.dropout
        config.batch_size = args.batch_size
        config.learning_rate = args.learning_rate
        config.train_epochs = args.train_epochs
        config.patience = args.patience
        config.use_gpu = args.use_gpu
        config.gpu = args.gpu
        
        # 设置数据路径
        config.anomaly_data_path = str(anomaly_path)
        config.earlysignal_data_path = str(earlysignal_path)
        
        print("\n开始训练...")
        
        if args.mode == 'anomaly':
            print("训练模式: 仅异常检测")
            # 异常检测训练
            config.task_name = 'anomaly_detection'
            exp = Exp_Fusion(config)
            exp.train()
            
        elif args.mode == 'earlysignal':
            print("训练模式: 仅前驱信号检测")
            # 前驱信号检测训练
            config.task_name = 'earlysignaldet'
            exp = Exp_Fusion(config)
            exp.train()
            
        else:  # both
            print("训练模式: 融合训练")
            
            # 先训练异常检测分支
            print("\n第一阶段: 训练异常检测分支")
            config.task_name = 'anomaly_detection'
            exp_anomaly = Exp_Fusion(config)
            exp_anomaly.train()
            
            # 再训练前驱信号检测分支
            print("\n第二阶段: 训练前驱信号检测分支")
            config.task_name = 'earlysignaldet'
            exp_earlysignal = Exp_Fusion(config)
            exp_earlysignal.train()
            
            # 联合微调（可选）
            print("\n第三阶段: 联合微调")
            config.task_name = 'fusion'
            config.train_epochs = args.train_epochs // 2  # 减少微调轮数
            exp_fusion = Exp_Fusion(config)
            exp_fusion.train()
        
        print("\n训练完成！")
        print(f"模型保存在: {args.checkpoints}")
        print(f"结果保存在: {args.results}")
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保所有必要的模块都已正确实现")
        print("您可以先运行快速验证脚本检查环境")
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
