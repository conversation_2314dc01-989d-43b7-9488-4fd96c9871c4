import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import PatternFill

# 读取csv文件，指定编码格式
df = pd.read_csv("D:/卡钻/Time-Series-Library-main/predictions.csv", encoding='ISO-8859-1')  # 或者尝试 'GBK'

# 定义一个函数来标记相邻5个时间段里有4个1的行
def mark_red(df):
    # 添加一列来判断是否满足条件
    df['Flag'] = df['Predicted_Label'].rolling(window=5).apply(lambda x: sum(x == 1) >= 4, raw=True).shift(-4)

    return df

# 标记数据
df_marked = mark_red(df)

# 将结果保存为Excel文件
excel_filename = "marked_output.xlsx"
df_marked.to_excel(excel_filename, index=False)

# 将结果保存为 CSV 文件
df_marked.to_csv("D:/卡钻/Time-Series-Library-main/predictions2.csv", index=False)

# 打开保存的Excel文件
wb = load_workbook(excel_filename)
ws = wb.active

# 设置标红的样式
red_fill = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")

# 根据标记的Flag列将满足条件的行标红
for row in ws.iter_rows(min_row=2, max_row=ws.max_row):  # 跳过第一行（标题）
    if row[0].row < len(df_marked) and df_marked.loc[row[0].row - 2, 'Flag']:  # 检查Flag值是否为True
        for cell in row:
            cell.fill = red_fill

# 保存修改后的Excel文件
wb.save(excel_filename)

print(f"标记完成，结果保存在 {excel_filename} 和 predictions2.csv")
