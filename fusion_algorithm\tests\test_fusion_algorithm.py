#!/usr/bin/env python3
"""
融合算法测试验证框架
专注于算法准确性和逻辑正确性的验证
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import unittest
from pathlib import Path
import tempfile
import shutil
from datetime import datetime, timedelta

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data_provider.fusion_data_loader import FusionDataLoader
from models.fusion_model import FusionModel, FusionLoss
from exp.exp_fusion import Exp_Fusion
from utils.warning_fusion import WarningFusion, WarningPeriod
from configs.fusion_config import FusionConfig


class TestFusionAlgorithm(unittest.TestCase):
    """融合算法核心测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.test_data_dir = tempfile.mkdtemp()
        cls.config = FusionConfig()
        cls.config.root_path = cls.test_data_dir + "/"
        cls.config.batch_size = 2
        cls.config.seq_len = 96
        cls.config.train_epochs = 2  # 减少训练轮数用于测试
        
        # 创建测试数据
        cls._create_test_data()
        
    @classmethod
    def tearDownClass(cls):
        """清理测试数据"""
        if os.path.exists(cls.test_data_dir):
            shutil.rmtree(cls.test_data_dir)
    
    @classmethod
    def _create_test_data(cls):
        """创建测试数据集"""
        # 创建目录结构
        anomaly_dir = Path(cls.test_data_dir) / "anomaly"
        anomaly_train_dir = anomaly_dir / "train"
        earlysignal_dir = Path(cls.test_data_dir) / "earlysignal"
        normal_dir = earlysignal_dir / "正常数据"
        precursor_dir = earlysignal_dir / "前驱"
        
        for dir_path in [anomaly_train_dir, normal_dir, precursor_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 创建异常检测测试数据
        cls._create_anomaly_test_data(anomaly_dir, anomaly_train_dir)
        
        # 创建前驱信号检测测试数据
        cls._create_earlysignal_test_data(normal_dir, precursor_dir)
    
    @classmethod
    def _create_anomaly_test_data(cls, anomaly_dir, train_dir):
        """创建异常检测测试数据"""
        # 生成训练数据 (CSV格式)
        for i in range(3):
            train_data = cls._generate_time_series_csv(
                length=200, 
                features=11, 
                anomaly_ratio=0.0,
                start_time=datetime(2024, 1, 1, 0, 0, 0)
            )
            train_data.to_csv(train_dir / f"train_file_{i}.csv", index=False)
        
        # 生成测试数据 (包含异常)
        test_data = cls._generate_time_series_csv(
            length=300, 
            features=11, 
            anomaly_ratio=0.1,
            start_time=datetime(2024, 1, 2, 0, 0, 0)
        )
        test_data.to_csv(anomaly_dir / "test_data.csv", index=False)
    
    @classmethod
    def _create_earlysignal_test_data(cls, normal_dir, precursor_dir):
        """创建前驱信号检测测试数据"""
        # 生成正常数据
        for i in range(5):
            normal_data = cls._generate_time_series_csv(
                length=90,
                features=11,
                anomaly_ratio=0.0,
                start_time=datetime(2024, 1, 1, i, 0, 0)
            )
            filename = f"1井_2024-01-01_{i:02d}-00-00_to_2024-01-01_{i:02d}-03-00.csv"
            normal_data.to_csv(normal_dir / filename, index=False)
        
        # 生成前驱信号数据
        for i in range(5):
            precursor_data = cls._generate_time_series_csv(
                length=90,
                features=11,
                anomaly_ratio=0.2,  # 前驱信号有更多异常模式
                start_time=datetime(2024, 1, 2, i, 0, 0)
            )
            filename = f"1井_2024-01-02_{i:02d}-00-00_to_2024-01-02_{i:02d}-03-00.csv"
            precursor_data.to_csv(precursor_dir / filename, index=False)
    
    @classmethod
    def _generate_time_series_csv(cls, length, features, anomaly_ratio=0.0, start_time=None):
        """生成时间序列CSV数据"""
        if start_time is None:
            start_time = datetime.now()
        
        # 生成时间戳
        timestamps = [start_time + timedelta(seconds=i*2) for i in range(length)]
        
        # 生成基础数据
        np.random.seed(42)  # 确保可重现性
        data = {
            'date': timestamps,
            'DEP': np.random.normal(5650, 10, length),
            'BITDEP': np.random.normal(5640, 10, length),
            'HOKHEI': np.random.normal(12, 2, length),
            'DRITIME': np.full(length, 18),
            'WOB': np.random.normal(15, 5, length),
            'HKLD': np.random.normal(1200, 50, length),
            'RPM': np.random.normal(60, 5, length),
            'TOR': np.random.normal(16, 3, length),
            'SPP': np.random.normal(31, 2, length),
            'CSIP': np.zeros(length)
        }
        
        # 添加异常
        if anomaly_ratio > 0:
            anomaly_count = int(length * anomaly_ratio)
            anomaly_indices = np.random.choice(length, anomaly_count, replace=False)
            
            for idx in anomaly_indices:
                # 在多个特征上添加异常
                data['WOB'][idx] *= 2.5  # 钻压异常
                data['TOR'][idx] *= 1.8  # 扭矩异常
                data['SPP'][idx] *= 1.5  # 泵压异常
        
        return pd.DataFrame(data)


class TestDataLoader(TestFusionAlgorithm):
    """数据加载器测试"""
    
    def test_fusion_data_loader_initialization(self):
        """测试融合数据加载器初始化"""
        try:
            loader = FusionDataLoader(
                root_path=self.config.root_path,
                win_size=self.config.seq_len,
                flag="train",
                task_type="fusion"
            )
            self.assertIsNotNone(loader)
            print("✓ 融合数据加载器初始化成功")
        except Exception as e:
            self.fail(f"融合数据加载器初始化失败: {e}")
    
    def test_anomaly_data_loading(self):
        """测试异常检测数据加载"""
        try:
            loader = FusionDataLoader(
                root_path=self.config.root_path,
                win_size=self.config.seq_len,
                flag="train",
                task_type="anomaly_detection"
            )
            
            # 检查数据维度
            self.assertGreater(len(loader), 0, "训练数据不能为空")
            
            # 测试数据获取
            sample_x, sample_y, timestamps = loader[0]
            self.assertEqual(sample_x.shape[0], self.config.seq_len, "序列长度不匹配")
            self.assertEqual(sample_x.shape[1], 11, "特征维度不匹配")
            
            print(f"✓ 异常检测数据加载成功: {sample_x.shape}")
        except Exception as e:
            self.fail(f"异常检测数据加载失败: {e}")
    
    def test_earlysignal_data_loading(self):
        """测试前驱信号数据加载"""
        try:
            loader = FusionDataLoader(
                root_path=self.config.root_path,
                win_size=self.config.seq_len,
                flag="train",
                task_type="earlysignaldet"
            )
            
            # 检查数据维度
            self.assertGreater(len(loader), 0, "前驱信号数据不能为空")
            
            # 测试数据获取
            sample_x, sample_y, file_path = loader[0]
            self.assertEqual(sample_x.shape[0], self.config.seq_len, "序列长度不匹配")
            self.assertEqual(sample_x.shape[1], 11, "特征维度不匹配")
            self.assertIn(sample_y.item(), [0, 1], "标签必须是0或1")
            
            print(f"✓ 前驱信号数据加载成功: {sample_x.shape}, 标签: {sample_y.item()}")
        except Exception as e:
            self.fail(f"前驱信号数据加载失败: {e}")


class TestFusionModel(TestFusionAlgorithm):
    """融合模型测试"""
    
    def setUp(self):
        """每个测试前的设置"""
        self.model = FusionModel(self.config)
        self.criterion = FusionLoss()
    
    def test_model_initialization(self):
        """测试模型初始化"""
        self.assertIsNotNone(self.model)
        self.assertIsNotNone(self.criterion)
        print("✓ 融合模型初始化成功")
    
    def test_anomaly_detection_forward(self):
        """测试异常检测前向传播"""
        batch_size = 2
        seq_len = self.config.seq_len
        features = 11
        
        # 创建测试输入
        x = torch.randn(batch_size, seq_len, features)
        
        # 前向传播
        output = self.model(x, task_mode="anomaly")
        
        # 验证输出维度
        self.assertEqual(output.shape, (batch_size, seq_len, features), 
                        f"异常检测输出维度错误: {output.shape}")
        
        # 验证损失计算
        loss = self.criterion(output, x, task_mode="anomaly")
        self.assertIsInstance(loss.item(), float, "损失必须是标量")
        self.assertGreater(loss.item(), 0, "损失必须大于0")
        
        print(f"✓ 异常检测前向传播成功: 输出维度 {output.shape}, 损失 {loss.item():.4f}")
    
    def test_earlysignal_detection_forward(self):
        """测试前驱信号检测前向传播"""
        batch_size = 2
        seq_len = self.config.seq_len
        features = 11
        
        # 创建测试输入
        x = torch.randn(batch_size, seq_len, features)
        y = torch.randint(0, 2, (batch_size, 1))
        
        # 前向传播
        output = self.model(x, task_mode="earlysignal")
        
        # 验证输出维度
        self.assertEqual(output.shape, (batch_size, 2), 
                        f"前驱信号检测输出维度错误: {output.shape}")
        
        # 验证损失计算
        loss = self.criterion(output, y, task_mode="earlysignal")
        self.assertIsInstance(loss.item(), float, "损失必须是标量")
        self.assertGreater(loss.item(), 0, "损失必须大于0")
        
        # 验证概率输出
        probs = torch.softmax(output, dim=1)
        self.assertTrue(torch.allclose(probs.sum(dim=1), torch.ones(batch_size)), 
                       "概率和必须等于1")
        
        print(f"✓ 前驱信号检测前向传播成功: 输出维度 {output.shape}, 损失 {loss.item():.4f}")


class TestWarningFusion(TestFusionAlgorithm):
    """预警融合测试"""
    
    def setUp(self):
        """每个测试前的设置"""
        self.warning_fusion = WarningFusion(min_duration_minutes=1.0, merge_gap_minutes=5.0)
    
    def test_warning_period_creation(self):
        """测试预警时间段创建"""
        start_time = datetime(2024, 1, 1, 10, 0, 0)
        end_time = datetime(2024, 1, 1, 10, 5, 0)
        
        warning = WarningPeriod(start_time, end_time, "anomaly", confidence=0.8)
        
        self.assertEqual(warning.start_time, pd.to_datetime(start_time))
        self.assertEqual(warning.end_time, pd.to_datetime(end_time))
        self.assertEqual(warning.warning_type, "anomaly")
        self.assertEqual(warning.confidence, 0.8)
        self.assertEqual(warning.duration_minutes, 5.0)
        
        print("✓ 预警时间段创建成功")
    
    def test_anomaly_warning_extraction(self):
        """测试异常检测预警提取"""
        # 创建测试异常检测结果
        timestamps = pd.date_range('2024-01-01 10:00:00', periods=100, freq='1min')
        anomaly_results = pd.DataFrame({
            'Timestamp': timestamps,
            'Anomaly_Score': np.random.uniform(0.1, 0.9, 100),
            'Predicted_Anomaly': [1 if i in range(20, 30) or i in range(60, 75) else 0 for i in range(100)]
        })
        
        warnings = self.warning_fusion.extract_anomaly_warnings(anomaly_results, min_continuous_minutes=5.0)
        
        self.assertGreater(len(warnings), 0, "应该提取到异常预警")
        
        for warning in warnings:
            self.assertEqual(warning.warning_type, "anomaly")
            self.assertGreaterEqual(warning.duration_minutes, 5.0)
            self.assertGreater(warning.confidence, 0)
        
        print(f"✓ 异常预警提取成功: 提取到 {len(warnings)} 个预警时间段")
    
    def test_earlysignal_warning_extraction(self):
        """测试前驱信号预警提取"""
        # 创建测试前驱信号检测结果
        earlysignal_results = pd.DataFrame({
            'Filename': [
                '1井_2024-01-01_10-00-00_to_2024-01-01_10-03-00.csv',
                '1井_2024-01-01_10-03-00_to_2024-01-01_10-06-00.csv',
                '1井_2024-01-01_10-06-00_to_2024-01-01_10-09-00.csv',
                '1井_2024-01-01_10-15-00_to_2024-01-01_10-18-00.csv'
            ],
            'Risk': [0.8, 0.9, 0.7, 0.6],
            'Predicted_Label': [1, 1, 1, 0]
        })
        
        warnings = self.warning_fusion.extract_earlysignal_warnings(
            earlysignal_results, min_risk_threshold=0.7
        )
        
        self.assertGreater(len(warnings), 0, "应该提取到前驱信号预警")
        
        for warning in warnings:
            self.assertEqual(warning.warning_type, "earlysignal")
            self.assertGreaterEqual(warning.confidence, 0.7)
        
        print(f"✓ 前驱信号预警提取成功: 提取到 {len(warnings)} 个预警时间段")


if __name__ == '__main__':
    print("=" * 60)
    print("融合算法测试验证框架")
    print("=" * 60)
    
    # 运行测试
    unittest.main(verbosity=2, exit=False)
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
