#!/usr/bin/env python3
"""
快速验证脚本
使用真实数据进行基本的功能验证和性能测试
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 添加layers模块路径
layers_path = project_root.parent / "异常检测/Time-Series-Library-main-loss/Time-Series-Library-main"
if layers_path.exists():
    sys.path.insert(0, str(layers_path))

try:
    from data_provider.fusion_data_loader import FusionDataLoader
    from models.fusion_model import FusionModel
    from exp.exp_fusion import Exp_Fusion
    from utils.warning_fusion import WarningFusion
    from configs.fusion_config import FusionConfig
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("尝试使用简化版本进行基本验证...")

    # 创建简化的配置类
    class FusionConfig:
        def __init__(self):
            self.seq_len = 96
            self.d_model = 128
            self.patch_len = 16
            self.stride = 8
            self.dropout = 0.1
            self.n_heads = 8
            self.e_layers = 3
            self.d_ff = 256

    # 创建简化的模型类用于基本验证
    class FusionModel(torch.nn.Module):
        def __init__(self, config):
            super().__init__()
            self.config = config
            self.encoder = torch.nn.TransformerEncoder(
                torch.nn.TransformerEncoderLayer(
                    d_model=config.d_model,
                    nhead=config.n_heads,
                    dim_feedforward=config.d_ff,
                    dropout=config.dropout,
                    batch_first=True
                ),
                num_layers=config.e_layers
            )
            self.input_projection = torch.nn.Linear(11, config.d_model)
            self.anomaly_head = torch.nn.Linear(config.d_model, 11)
            self.earlysignal_head = torch.nn.Sequential(
                torch.nn.AdaptiveAvgPool1d(1),
                torch.nn.Flatten(),
                torch.nn.Linear(config.d_model, 2)
            )

        def forward(self, x, task_mode="anomaly"):
            # x: [batch, seq_len, features]
            x = self.input_projection(x)  # [batch, seq_len, d_model]
            x = self.encoder(x)  # [batch, seq_len, d_model]

            if task_mode == "anomaly":
                return self.anomaly_head(x)  # [batch, seq_len, features]
            else:  # earlysignal
                x = x.transpose(1, 2)  # [batch, d_model, seq_len]
                return self.earlysignal_head(x)  # [batch, 2]

    # 创建简化的预警融合类
    class WarningFusion:
        def __init__(self, min_duration_minutes=1.0, merge_gap_minutes=5.0):
            self.min_duration_minutes = min_duration_minutes
            self.merge_gap_minutes = merge_gap_minutes

        def extract_anomaly_warnings(self, results, min_continuous_minutes=2.0):
            return []  # 简化实现

        def extract_earlysignal_warnings(self, results, min_risk_threshold=0.7):
            return []  # 简化实现

        def merge_warnings(self, warnings):
            return warnings  # 简化实现

        def generate_fusion_report(self, warnings):
            return pd.DataFrame()  # 简化实现


class QuickValidator:
    """快速验证器"""
    
    def __init__(self):
        self.config = FusionConfig()
        self.project_root = Path(__file__).parent.parent.parent
        self.results = {
            'data_loading': {'status': 'pending', 'details': {}},
            'model_creation': {'status': 'pending', 'details': {}},
            'forward_pass': {'status': 'pending', 'details': {}},
            'training_step': {'status': 'pending', 'details': {}},
            'inference': {'status': 'pending', 'details': {}},
            'warning_fusion': {'status': 'pending', 'details': {}}
        }
        
        # 设置数据路径
        self._setup_data_paths()
    
    def _setup_data_paths(self):
        """设置数据路径"""
        # 异常检测数据路径
        self.anomaly_data_path = self.project_root / "异常检测/Time-Series-Library-main-loss/Time-Series-Library-main/dataset"
        
        # 前驱信号数据路径
        self.earlysignal_data_path = self.project_root / "前驱信号检测/dataset"
        
        print(f"异常检测数据路径: {self.anomaly_data_path}")
        print(f"前驱信号数据路径: {self.earlysignal_data_path}")
    
    def validate_data_loading(self):
        """验证数据加载"""
        print("\n" + "="*50)
        print("1. 验证数据加载")
        print("="*50)
        
        try:
            # 检查数据路径存在性
            if not self.anomaly_data_path.exists():
                raise FileNotFoundError(f"异常检测数据路径不存在: {self.anomaly_data_path}")
            
            if not self.earlysignal_data_path.exists():
                raise FileNotFoundError(f"前驱信号数据路径不存在: {self.earlysignal_data_path}")
            
            # 尝试加载异常检测数据
            print("加载异常检测数据...")
            anomaly_files = list(self.anomaly_data_path.glob("**/*实时数据.npy"))
            if anomaly_files:
                try:
                    # 尝试使用allow_pickle=True加载
                    sample_data = np.load(anomaly_files[0], allow_pickle=True)
                    print(f"✓ 异常检测数据加载成功: {sample_data.shape}")
                    self.results['data_loading']['details']['anomaly_shape'] = sample_data.shape
                except Exception as e:
                    print(f"⚠ 异常检测数据加载失败: {e}")
                    # 尝试其他文件
                    for file in anomaly_files[:3]:  # 尝试前3个文件
                        try:
                            sample_data = np.load(file, allow_pickle=True)
                            print(f"✓ 异常检测数据加载成功 (备用文件): {sample_data.shape}")
                            self.results['data_loading']['details']['anomaly_shape'] = sample_data.shape
                            break
                        except:
                            continue
                    else:
                        print("⚠ 所有异常检测数据文件都无法加载")
            else:
                print("⚠ 未找到异常检测数据文件")
            
            # 尝试加载前驱信号数据
            print("加载前驱信号数据...")
            normal_files = list(self.earlysignal_data_path.glob("**/normal/*.csv"))
            precursor_files = list(self.earlysignal_data_path.glob("**/earlysignal*/*.csv"))
            
            if normal_files:
                sample_df = pd.read_csv(normal_files[0])
                print(f"✓ 正常数据加载成功: {sample_df.shape}")
                self.results['data_loading']['details']['normal_shape'] = sample_df.shape
            
            if precursor_files:
                sample_df = pd.read_csv(precursor_files[0])
                print(f"✓ 前驱数据加载成功: {sample_df.shape}")
                self.results['data_loading']['details']['precursor_shape'] = sample_df.shape
            
            self.results['data_loading']['status'] = 'success'
            print("✓ 数据加载验证通过")
            
        except Exception as e:
            self.results['data_loading']['status'] = 'failed'
            self.results['data_loading']['details']['error'] = str(e)
            print(f"✗ 数据加载验证失败: {e}")
    
    def validate_model_creation(self):
        """验证模型创建"""
        print("\n" + "="*50)
        print("2. 验证模型创建")
        print("="*50)
        
        try:
            # 创建模型
            print("创建融合模型...")
            self.model = FusionModel(self.config)
            
            # 检查模型参数
            total_params = sum(p.numel() for p in self.model.parameters())
            trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
            
            print(f"✓ 模型创建成功")
            print(f"  总参数数量: {total_params:,}")
            print(f"  可训练参数: {trainable_params:,}")
            
            self.results['model_creation']['status'] = 'success'
            self.results['model_creation']['details'] = {
                'total_params': total_params,
                'trainable_params': trainable_params
            }
            
        except Exception as e:
            self.results['model_creation']['status'] = 'failed'
            self.results['model_creation']['details']['error'] = str(e)
            print(f"✗ 模型创建失败: {e}")
    
    def validate_forward_pass(self):
        """验证前向传播"""
        print("\n" + "="*50)
        print("3. 验证前向传播")
        print("="*50)
        
        if self.results['model_creation']['status'] != 'success':
            print("⚠ 跳过前向传播验证（模型创建失败）")
            self.results['forward_pass']['status'] = 'skipped'
            return
        
        try:
            # 创建测试数据
            batch_size = 2
            seq_len = self.config.seq_len
            features = 11
            
            test_input = torch.randn(batch_size, seq_len, features)
            
            self.model.eval()
            with torch.no_grad():
                # 测试异常检测
                print("测试异常检测前向传播...")
                anomaly_output = self.model(test_input, task_mode="anomaly")
                print(f"✓ 异常检测输出形状: {anomaly_output.shape}")
                
                # 测试前驱信号检测
                print("测试前驱信号检测前向传播...")
                earlysignal_output = self.model(test_input, task_mode="earlysignal")
                print(f"✓ 前驱信号检测输出形状: {earlysignal_output.shape}")
                
                # 验证输出合理性
                self.assertEqual(anomaly_output.shape, (batch_size, seq_len, features))
                self.assertEqual(earlysignal_output.shape, (batch_size, 2))
                
                # 检查数值稳定性
                self.assertFalse(torch.isnan(anomaly_output).any())
                self.assertFalse(torch.isnan(earlysignal_output).any())
                
            self.results['forward_pass']['status'] = 'success'
            self.results['forward_pass']['details'] = {
                'anomaly_output_shape': list(anomaly_output.shape),
                'earlysignal_output_shape': list(earlysignal_output.shape)
            }
            print("✓ 前向传播验证通过")
            
        except Exception as e:
            self.results['forward_pass']['status'] = 'failed'
            self.results['forward_pass']['details']['error'] = str(e)
            print(f"✗ 前向传播验证失败: {e}")
    
    def validate_training_step(self):
        """验证训练步骤"""
        print("\n" + "="*50)
        print("4. 验证训练步骤")
        print("="*50)
        
        if self.results['model_creation']['status'] != 'success':
            print("⚠ 跳过训练步骤验证（模型创建失败）")
            self.results['training_step']['status'] = 'skipped'
            return
        
        try:
            # 创建优化器和损失函数
            try:
                from models.fusion_model import FusionLoss
                criterion = FusionLoss()
            except ImportError:
                # 使用简化的损失函数
                class FusionLoss(torch.nn.Module):
                    def __init__(self):
                        super().__init__()
                        self.mse_loss = torch.nn.MSELoss()
                        self.ce_loss = torch.nn.CrossEntropyLoss()

                    def forward(self, pred, target, task_mode="anomaly"):
                        if task_mode == "anomaly":
                            return self.mse_loss(pred, target)
                        else:  # earlysignal
                            return self.ce_loss(pred, target.squeeze())

                criterion = FusionLoss()

            optimizer = torch.optim.Adam(self.model.parameters(), lr=0.001)
            
            # 创建训练数据
            batch_size = 2
            seq_len = self.config.seq_len
            features = 11
            
            x = torch.randn(batch_size, seq_len, features)
            y_anomaly = x + torch.randn_like(x) * 0.1
            y_earlysignal = torch.randint(0, 2, (batch_size, 1))
            
            self.model.train()
            
            # 测试异常检测训练步骤
            print("测试异常检测训练步骤...")
            optimizer.zero_grad()
            anomaly_pred = self.model(x, task_mode="anomaly")
            anomaly_loss = criterion(anomaly_pred, y_anomaly, task_mode="anomaly")
            anomaly_loss.backward()
            optimizer.step()
            
            print(f"✓ 异常检测损失: {anomaly_loss.item():.6f}")
            
            # 测试前驱信号检测训练步骤
            print("测试前驱信号检测训练步骤...")
            optimizer.zero_grad()
            earlysignal_pred = self.model(x, task_mode="earlysignal")
            earlysignal_loss = criterion(earlysignal_pred, y_earlysignal, task_mode="earlysignal")
            earlysignal_loss.backward()
            optimizer.step()
            
            print(f"✓ 前驱信号检测损失: {earlysignal_loss.item():.6f}")
            
            self.results['training_step']['status'] = 'success'
            self.results['training_step']['details'] = {
                'anomaly_loss': anomaly_loss.item(),
                'earlysignal_loss': earlysignal_loss.item()
            }
            print("✓ 训练步骤验证通过")
            
        except Exception as e:
            self.results['training_step']['status'] = 'failed'
            self.results['training_step']['details']['error'] = str(e)
            print(f"✗ 训练步骤验证失败: {e}")
    
    def validate_inference(self):
        """验证推理过程"""
        print("\n" + "="*50)
        print("5. 验证推理过程")
        print("="*50)
        
        if self.results['model_creation']['status'] != 'success':
            print("⚠ 跳过推理验证（模型创建失败）")
            self.results['inference']['status'] = 'skipped'
            return
        
        try:
            # 模拟推理过程
            print("模拟异常检测推理...")
            
            # 创建模拟的异常检测结果
            timestamps = pd.date_range('2024-01-01 10:00:00', periods=100, freq='2S')
            anomaly_scores = np.random.uniform(0.1, 0.9, 100)
            anomaly_predictions = (anomaly_scores > 0.7).astype(int)
            
            anomaly_results = pd.DataFrame({
                'Timestamp': timestamps,
                'Anomaly_Score': anomaly_scores,
                'Predicted_Anomaly': anomaly_predictions
            })
            
            print(f"✓ 异常检测结果: {len(anomaly_results)} 个时间点")
            print(f"  异常点数量: {anomaly_predictions.sum()}")
            
            # 模拟前驱信号检测推理
            print("模拟前驱信号检测推理...")
            
            earlysignal_results = pd.DataFrame({
                'Filename': [
                    '1井_2024-01-01_10-00-00_to_2024-01-01_10-03-00.csv',
                    '1井_2024-01-01_10-03-00_to_2024-01-01_10-06-00.csv',
                    '1井_2024-01-01_10-06-00_to_2024-01-01_10-09-00.csv'
                ],
                'Risk': [0.8, 0.6, 0.9],
                'Predicted_Label': [1, 0, 1]
            })
            
            print(f"✓ 前驱信号检测结果: {len(earlysignal_results)} 个文件")
            print(f"  高风险文件数量: {(earlysignal_results['Predicted_Label'] == 1).sum()}")
            
            self.results['inference']['status'] = 'success'
            self.results['inference']['details'] = {
                'anomaly_points': int(anomaly_predictions.sum()),
                'total_anomaly_samples': len(anomaly_results),
                'high_risk_files': int((earlysignal_results['Predicted_Label'] == 1).sum()),
                'total_earlysignal_files': len(earlysignal_results)
            }
            print("✓ 推理过程验证通过")
            
        except Exception as e:
            self.results['inference']['status'] = 'failed'
            self.results['inference']['details']['error'] = str(e)
            print(f"✗ 推理过程验证失败: {e}")
    
    def validate_warning_fusion(self):
        """验证预警融合"""
        print("\n" + "="*50)
        print("6. 验证预警融合")
        print("="*50)
        
        try:
            # 创建预警融合器
            warning_fusion = WarningFusion(min_duration_minutes=1.0, merge_gap_minutes=5.0)
            
            # 创建模拟的异常检测结果
            timestamps = pd.date_range('2024-01-01 10:00:00', periods=200, freq='30S')
            anomaly_predictions = np.zeros(200)
            anomaly_predictions[50:70] = 1  # 异常段1
            anomaly_predictions[120:140] = 1  # 异常段2
            
            anomaly_results = pd.DataFrame({
                'Timestamp': timestamps,
                'Anomaly_Score': np.random.uniform(0.5, 0.9, 200),
                'Predicted_Anomaly': anomaly_predictions
            })
            
            # 提取异常预警
            print("提取异常预警...")
            anomaly_warnings = warning_fusion.extract_anomaly_warnings(
                anomaly_results, min_continuous_minutes=2.0
            )
            print(f"✓ 提取到 {len(anomaly_warnings)} 个异常预警时间段")
            
            # 创建模拟的前驱信号检测结果
            earlysignal_results = pd.DataFrame({
                'Filename': [
                    '1井_2024-01-01_10-00-00_to_2024-01-01_10-03-00.csv',
                    '1井_2024-01-01_10-15-00_to_2024-01-01_10-18-00.csv',
                    '1井_2024-01-01_10-30-00_to_2024-01-01_10-33-00.csv'
                ],
                'Risk': [0.8, 0.9, 0.7],
                'Predicted_Label': [1, 1, 1]
            })
            
            # 提取前驱信号预警
            print("提取前驱信号预警...")
            earlysignal_warnings = warning_fusion.extract_earlysignal_warnings(
                earlysignal_results, min_risk_threshold=0.7
            )
            print(f"✓ 提取到 {len(earlysignal_warnings)} 个前驱信号预警时间段")
            
            # 融合预警
            print("融合预警时间段...")
            all_warnings = anomaly_warnings + earlysignal_warnings
            merged_warnings = warning_fusion.merge_warnings(all_warnings)
            
            print(f"✓ 融合后预警时间段: {len(merged_warnings)} 个")
            
            # 生成报告
            report = warning_fusion.generate_fusion_report(merged_warnings)
            print(f"✓ 生成融合报告: {len(report)} 行")
            
            self.results['warning_fusion']['status'] = 'success'
            self.results['warning_fusion']['details'] = {
                'anomaly_warnings': len(anomaly_warnings),
                'earlysignal_warnings': len(earlysignal_warnings),
                'merged_warnings': len(merged_warnings),
                'report_lines': len(report)
            }
            print("✓ 预警融合验证通过")
            
        except Exception as e:
            self.results['warning_fusion']['status'] = 'failed'
            self.results['warning_fusion']['details']['error'] = str(e)
            print(f"✗ 预警融合验证失败: {e}")
    
    def assertEqual(self, a, b, msg=""):
        """简单的断言方法"""
        if a != b:
            raise AssertionError(f"{msg}: {a} != {b}")
    
    def assertFalse(self, condition, msg=""):
        """简单的断言方法"""
        if condition:
            raise AssertionError(f"{msg}: condition is True")
    
    def run_all_validations(self):
        """运行所有验证"""
        print("融合算法快速验证")
        print("=" * 80)
        
        start_time = datetime.now()
        
        # 依次运行各项验证
        self.validate_data_loading()
        self.validate_model_creation()
        self.validate_forward_pass()
        self.validate_training_step()
        self.validate_inference()
        self.validate_warning_fusion()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 打印总结
        self.print_summary(duration)
    
    def print_summary(self, duration):
        """打印验证总结"""
        print("\n" + "="*80)
        print("验证结果总结")
        print("="*80)
        
        print(f"总验证时间: {duration:.2f} 秒")
        print()
        
        success_count = 0
        total_count = 0
        
        for test_name, result in self.results.items():
            total_count += 1
            status = result['status']
            
            if status == 'success':
                icon = "✓"
                success_count += 1
            elif status == 'failed':
                icon = "✗"
            elif status == 'skipped':
                icon = "-"
            else:
                icon = "?"
            
            print(f"{icon} {test_name.replace('_', ' ').title()}: {status.upper()}")
        
        print()
        print(f"成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        # 给出建议
        if success_count == total_count:
            print("\n🎉 所有验证通过！算法基本功能正常，可以进行进一步测试。")
        else:
            print(f"\n⚠ 有 {total_count - success_count} 项验证未通过，建议检查相关实现。")


def main():
    """主函数"""
    validator = QuickValidator()
    validator.run_all_validations()


if __name__ == '__main__':
    main()
