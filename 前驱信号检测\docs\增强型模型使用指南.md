# 增强型钻井卡钻预警模型使用指南

## 概述

本文档提供了使用增强型钻井卡钻预警模型(EnhancedPatchTST)的详细说明。该模型融合了机器学习方法与专家知识规则，旨在提高钻井过程中卡钻事件的预测准确性。

## 系统要求

- Python 3.7+
- PyTorch 1.7+
- NumPy, Pandas, Scikit-learn
- Matplotlib (用于可视化)

## 模型特点

1. **专家知识融合**：将领域专家定义的规则与深度学习模型相结合
2. **时间序列处理**：基于PatchTST架构，针对钻井时序数据进行优化
3. **风险评估**：提供融合后的风险评分，更客观地反映实际风险

## 数据准备

数据应存放在以下目录结构中：

```
dataset/
├── earlysignaldet/
│   ├── normal/          # 正常钻井数据
│   │   └── *.csv
│   └── earlysignal2/    # 含有前驱信号的数据
│       └── *.csv
└── predicate/           # 预测用数据集
    └── *.csv
```

### 数据格式要求

CSV文件应包含以下主要列：

- 时间戳（可选）
- 井深
- 钩负荷
- 钻压
- 转速
- 扭矩
- 立管压力
- 机械钻速
- ... (其他相关特征)

## 配置参数说明

增强型模型引入了以下重要参数：

- `expert_weight`：专家规则在最终预测中的权重（默认为0.3）
- `expert_threshold`：触发专家规则的阈值（默认为4）
- `expert_window`：提取专家特征的窗口大小（默认为20）

其他主要参数：

- `model`：模型名称，使用`EnhancedPatchTST`
- `seq_len`：输入序列长度
- `n_layers`：Transformer层数
- `d_model`：模型维度
- `batch_size`：训练批次大小
- `learning_rate`：学习率

## 快速开始

### 训练模型

可以使用提供的示例脚本进行模型训练：

```bash
bash run_enhanced_example.sh
```

或者直接运行Python命令：

```bash
python run_enhanced.py \
  --model EnhancedPatchTST \
  --model_id "enhanced_patchtst" \
  --task_name earlysignaldet \
  --is_training True \
  --root_path "./dataset/" \
  --features M \
  --seq_len 64 \
  --n_layers 3 \
  --expert_weight 0.3 \
  --expert_threshold 4 \
  --expert_window 20
```

### 测试与预测

```bash
python run_enhanced.py \
  --model EnhancedPatchTST \
  --model_id "enhanced_patchtst" \
  --task_name earlysignaldet \
  --is_training False \
  --root_path "./dataset/" \
  --expert_weight 0.3 \
  --expert_threshold 4
```

## 结果解释

模型会生成以下输出：

1. **训练过程**：训练和验证损失、准确率等指标
2. **测试结果**：精确率、召回率、F1分数和ROC曲线
3. **预测文件**：包含以下列：
   - `Filename`：文件名
   - `Risk_Fusion`：融合后的风险分数（0-1）
   - `Risk_Raw`：模型原始风险分数
   - `Predicted_Label`：预测的类别（0：正常，1：异常）
   - `ExpertScore`：专家规则得分

## 专家规则说明

当前实现的专家规则考虑以下指标的异常变化：

1. **钻压异常**：钻压快速变化或超过安全阈值
2. **扭矩变化**：扭矩在短时间内的异常增加
3. **机械钻速下降**：机械钻速的显著下降
4. **泵压波动**：泵压的不规则波动

系统根据这些因素计算一个综合风险分数，当分数超过`expert_threshold`时，将提高最终预测的风险评分。

## 性能调优

若要提高模型性能，可以尝试调整以下参数：

1. **专家权重（expert_weight）**：若专家经验更可靠，可提高此值；反之则降低
2. **专家阈值（expert_threshold）**：降低此值会增加警报敏感度，但可能增加误报率
3. **序列长度（seq_len）**：增加此值可考虑更长历史数据，但会增加计算量
4. **模型复杂度（n_layers, d_model）**：增加可提高模型表达能力，但需要更多数据

## 常见问题

1. **模型过拟合**：减小模型复杂度或增加dropout值
2. **误报过多**：调高expert_threshold，或减小expert_weight
3. **漏报过多**：调低expert_threshold，或增加expert_weight
4. **训练速度慢**：减小batch_size或降低模型复杂度

## 附录：专家特征提取详解

系统提取的主要专家特征包括：

1. **趋势特征**：使用滑动窗口计算关键参数的变化趋势和速率
2. **波动特征**：计算关键参数的波动范围和频率
3. **相对变化特征**：参数之间的相对变化关系
4. **阈值特征**：参数是否超过预设安全阈值

这些特征与基于机器学习的预测结果融合，形成最终的风险评估。 