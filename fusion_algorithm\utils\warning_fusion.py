import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import re
from typing import List, Dict, Tuple, Optional


class WarningPeriod:
    """预警时间段类"""
    def __init__(self, start_time, end_time, warning_type, confidence=1.0, source="", metadata=None):
        self.start_time = pd.to_datetime(start_time)
        self.end_time = pd.to_datetime(end_time)
        self.warning_type = warning_type  # 'anomaly' or 'earlysignal'
        self.confidence = confidence
        self.source = source
        self.metadata = metadata or {}
        self.duration_minutes = (self.end_time - self.start_time).total_seconds() / 60
    
    def __repr__(self):
        return f"WarningPeriod({self.warning_type}, {self.start_time} - {self.end_time}, conf={self.confidence:.3f})"
    
    def overlaps_with(self, other):
        """检查是否与另一个预警时间段重叠"""
        return not (self.end_time <= other.start_time or self.start_time >= other.end_time)
    
    def merge_with(self, other):
        """与另一个预警时间段合并"""
        start_time = min(self.start_time, other.start_time)
        end_time = max(self.end_time, other.end_time)
        
        # 合并置信度（加权平均）
        total_duration = self.duration_minutes + other.duration_minutes
        if total_duration > 0:
            confidence = (self.confidence * self.duration_minutes + 
                         other.confidence * other.duration_minutes) / total_duration
        else:
            confidence = (self.confidence + other.confidence) / 2
        
        # 合并类型
        if self.warning_type == other.warning_type:
            warning_type = self.warning_type
        else:
            warning_type = 'fusion'
        
        return WarningPeriod(start_time, end_time, warning_type, confidence, 
                           f"{self.source}+{other.source}")


class WarningFusion:
    """预警时间段融合器"""
    
    def __init__(self, min_duration_minutes=1.0, merge_gap_minutes=5.0):
        self.min_duration_minutes = min_duration_minutes
        self.merge_gap_minutes = merge_gap_minutes
    
    def extract_anomaly_warnings(self, anomaly_results: pd.DataFrame, 
                                min_continuous_minutes=5.0) -> List[WarningPeriod]:
        """
        从异常检测结果中提取预警时间段
        
        Args:
            anomaly_results: DataFrame包含['Timestamp', 'Anomaly_Score', 'Predicted_Anomaly']
            min_continuous_minutes: 最小连续异常时间（分钟）
        """
        warnings = []
        
        if len(anomaly_results) == 0:
            return warnings
        
        # 确保时间戳格式正确
        anomaly_results = anomaly_results.copy()
        anomaly_results['Timestamp'] = pd.to_datetime(anomaly_results['Timestamp'])
        anomaly_results = anomaly_results.sort_values('Timestamp')
        
        # 查找连续异常段
        in_anomaly = False
        start_idx = None
        
        for i, row in anomaly_results.iterrows():
            if row['Predicted_Anomaly'] == 1:
                if not in_anomaly:
                    in_anomaly = True
                    start_idx = i
            else:
                if in_anomaly:
                    in_anomaly = False
                    end_idx = i - 1
                    
                    start_time = anomaly_results.loc[start_idx, 'Timestamp']
                    end_time = anomaly_results.loc[end_idx, 'Timestamp']
                    duration = (end_time - start_time).total_seconds() / 60
                    
                    if duration >= min_continuous_minutes:
                        avg_score = anomaly_results.loc[start_idx:end_idx, 'Anomaly_Score'].mean()
                        warnings.append(WarningPeriod(
                            start_time, end_time, 'anomaly', 
                            confidence=min(avg_score, 1.0),
                            source='anomaly_detection',
                            metadata={'avg_score': avg_score, 'duration': duration}
                        ))
        
        # 处理最后一个异常段
        if in_anomaly and start_idx is not None:
            end_idx = len(anomaly_results) - 1
            start_time = anomaly_results.loc[start_idx, 'Timestamp']
            end_time = anomaly_results.loc[end_idx, 'Timestamp']
            duration = (end_time - start_time).total_seconds() / 60
            
            if duration >= min_continuous_minutes:
                avg_score = anomaly_results.loc[start_idx:end_idx, 'Anomaly_Score'].mean()
                warnings.append(WarningPeriod(
                    start_time, end_time, 'anomaly',
                    confidence=min(avg_score, 1.0),
                    source='anomaly_detection',
                    metadata={'avg_score': avg_score, 'duration': duration}
                ))
        
        return warnings
    
    def extract_earlysignal_warnings(self, earlysignal_results: pd.DataFrame,
                                   min_risk_threshold=0.7) -> List[WarningPeriod]:
        """
        从前驱信号检测结果中提取预警时间段
        
        Args:
            earlysignal_results: DataFrame包含['Filename', 'Risk', 'Predicted_Label']
            min_risk_threshold: 最小风险阈值
        """
        warnings = []
        
        if len(earlysignal_results) == 0:
            return warnings
        
        # 从文件名提取时间信息
        def extract_time_from_filename(filename):
            """从文件名提取时间信息"""
            # 匹配格式：井号_YYYY-MM-DD_HH-MM-SS
            match = re.search(r'(\d+井)_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})', str(filename))
            if match:
                well_id = match.group(1)
                time_str = match.group(2).replace('_', ' ').replace('-', ':')
                try:
                    timestamp = pd.to_datetime(time_str, format='%Y:%m:%d %H:%M:%S')
                    return well_id, timestamp
                except:
                    pass
            return None, None
        
        # 提取时间信息并过滤高风险样本
        time_data = []
        for _, row in earlysignal_results.iterrows():
            well_id, timestamp = extract_time_from_filename(row['Filename'])
            if timestamp is not None and (row['Predicted_Label'] == 1 or row['Risk'] >= min_risk_threshold):
                time_data.append({
                    'well_id': well_id,
                    'timestamp': timestamp,
                    'risk': row['Risk'],
                    'predicted_label': row['Predicted_Label'],
                    'filename': row['Filename']
                })
        
        if not time_data:
            return warnings
        
        # 按井号和时间排序
        time_df = pd.DataFrame(time_data)
        time_df = time_df.sort_values(['well_id', 'timestamp'])
        
        # 按井号分组处理
        for well_id, group in time_df.groupby('well_id'):
            group = group.sort_values('timestamp')
            
            # 查找连续高风险时间段
            current_start = None
            current_risks = []
            
            for i, row in group.iterrows():
                if current_start is None:
                    current_start = row['timestamp']
                    current_risks = [row['risk']]
                else:
                    # 检查时间间隔
                    time_gap = (row['timestamp'] - group.loc[group.index[group.index.get_loc(i)-1], 'timestamp']).total_seconds() / 60
                    
                    if time_gap <= self.merge_gap_minutes:  # 连续
                        current_risks.append(row['risk'])
                    else:  # 间隔太大，结束当前段
                        if len(current_risks) > 0:
                            end_time = group.loc[group.index[group.index.get_loc(i)-1], 'timestamp']
                            avg_risk = np.mean(current_risks)
                            
                            warnings.append(WarningPeriod(
                                current_start, end_time, 'earlysignal',
                                confidence=avg_risk,
                                source='earlysignal_detection',
                                metadata={'well_id': well_id, 'avg_risk': avg_risk, 'sample_count': len(current_risks)}
                            ))
                        
                        current_start = row['timestamp']
                        current_risks = [row['risk']]
            
            # 处理最后一段
            if current_start is not None and len(current_risks) > 0:
                end_time = group.iloc[-1]['timestamp']
                avg_risk = np.mean(current_risks)
                
                warnings.append(WarningPeriod(
                    current_start, end_time, 'earlysignal',
                    confidence=avg_risk,
                    source='earlysignal_detection',
                    metadata={'well_id': well_id, 'avg_risk': avg_risk, 'sample_count': len(current_risks)}
                ))
        
        return warnings
    
    def merge_warnings(self, warnings: List[WarningPeriod]) -> List[WarningPeriod]:
        """合并重叠或相近的预警时间段"""
        if len(warnings) <= 1:
            return warnings
        
        # 按开始时间排序
        warnings = sorted(warnings, key=lambda w: w.start_time)
        merged = []
        
        current = warnings[0]
        
        for next_warning in warnings[1:]:
            # 检查是否需要合并
            time_gap = (next_warning.start_time - current.end_time).total_seconds() / 60
            
            if time_gap <= self.merge_gap_minutes or current.overlaps_with(next_warning):
                # 合并
                current = current.merge_with(next_warning)
            else:
                # 不合并，保存当前段并开始新段
                if current.duration_minutes >= self.min_duration_minutes:
                    merged.append(current)
                current = next_warning
        
        # 添加最后一段
        if current.duration_minutes >= self.min_duration_minutes:
            merged.append(current)
        
        return merged
    
    def fuse_warnings(self, anomaly_results: pd.DataFrame, 
                     earlysignal_results: pd.DataFrame,
                     anomaly_min_duration=5.0,
                     earlysignal_min_risk=0.7) -> List[WarningPeriod]:
        """
        融合两种算法的预警结果
        
        Args:
            anomaly_results: 异常检测结果
            earlysignal_results: 前驱信号检测结果
            anomaly_min_duration: 异常检测最小持续时间（分钟）
            earlysignal_min_risk: 前驱信号最小风险阈值
        
        Returns:
            融合后的预警时间段列表
        """
        # 提取各自的预警时间段
        anomaly_warnings = self.extract_anomaly_warnings(anomaly_results, anomaly_min_duration)
        earlysignal_warnings = self.extract_earlysignal_warnings(earlysignal_results, earlysignal_min_risk)
        
        print(f"提取到 {len(anomaly_warnings)} 个异常检测预警时间段")
        print(f"提取到 {len(earlysignal_warnings)} 个前驱信号预警时间段")
        
        # 合并所有预警
        all_warnings = anomaly_warnings + earlysignal_warnings
        
        # 合并重叠时间段
        fused_warnings = self.merge_warnings(all_warnings)
        
        print(f"融合后得到 {len(fused_warnings)} 个预警时间段")
        
        return fused_warnings
    
    def export_warnings_to_csv(self, warnings: List[WarningPeriod], output_path: str):
        """导出预警时间段到CSV文件"""
        if not warnings:
            print("没有预警时间段需要导出")
            return
        
        data = []
        for i, warning in enumerate(warnings):
            data.append({
                'Warning_ID': i + 1,
                'Start_Time': warning.start_time,
                'End_Time': warning.end_time,
                'Duration_Minutes': warning.duration_minutes,
                'Warning_Type': warning.warning_type,
                'Confidence': warning.confidence,
                'Source': warning.source,
                'Metadata': str(warning.metadata)
            })
        
        df = pd.DataFrame(data)
        df.to_csv(output_path, index=False, encoding='utf-8')
        print(f"预警时间段已导出到: {output_path}")


def create_warning_fusion(min_duration_minutes=1.0, merge_gap_minutes=5.0):
    """创建预警融合器的工厂函数"""
    return WarningFusion(min_duration_minutes, merge_gap_minutes)
