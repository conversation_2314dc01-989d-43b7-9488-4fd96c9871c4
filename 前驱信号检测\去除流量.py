import os
import pandas as pd

# 输入文件夹（原始 CSV）
input_folder = "D:\卡钻\Time-Series-Library-main\dataset2/1"

# 输出文件夹（清洗后的 CSV）
output_folder = "D:\卡钻\Time-Series-Library-main\dataset2\去流量"

# 确保输出文件夹存在
os.makedirs(output_folder, exist_ok=True)

# 指定想要保留并排序的列
desired_columns = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP', 'date']

# 遍历输入文件夹中的所有 CSV 文件
for filename in os.listdir(input_folder):
    if filename.endswith(".csv"):
        file_path = os.path.join(input_folder, filename)
        print(f"正在处理: {filename}")

        # 尝试读取 CSV，优先尝试 UTF-8，如果失败则用 GBK
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
        except UnicodeDecodeError:
            df = pd.read_csv(file_path, encoding='GBK')

        # 删除 FLOWIN 和 FLOWOUT（如果存在）
        df = df.drop(columns=['FLOWIN', 'FLOWOUT'], errors='ignore')

        # 只保留存在于文件中的指定列并按顺序排列
        existing_columns = [col for col in desired_columns if col in df.columns]
        df = df[existing_columns]

        # 构造新的文件路径
        new_filename = filename.replace(".csv", "_cleaned.csv")
        new_file_path = os.path.join(output_folder, new_filename)

        # 保存处理后的结果到输出文件夹
        df.to_csv(new_file_path, index=False, encoding='utf-8-sig')

        print(f"已保存为: {new_file_path}")

print("✅ 全部文件处理完成！")
