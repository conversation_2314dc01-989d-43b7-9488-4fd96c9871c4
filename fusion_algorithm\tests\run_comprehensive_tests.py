#!/usr/bin/env python3
"""
综合测试运行器
执行所有测试并生成详细的测试报告
"""

import os
import sys
import unittest
import time
import traceback
from pathlib import Path
from datetime import datetime
import json

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入测试模块
from test_fusion_algorithm import TestFusionAlgorithm, TestDataLoader, TestFusionModel, TestWarningFusion
from test_data_validation import TestDataValidation
from test_algorithm_logic import TestAlgorithmLogic, TestAlgorithmAccuracy


class ComprehensiveTestRunner:
    """综合测试运行器"""
    
    def __init__(self):
        self.results = {
            'start_time': None,
            'end_time': None,
            'total_duration': 0,
            'test_suites': {},
            'summary': {
                'total_tests': 0,
                'passed_tests': 0,
                'failed_tests': 0,
                'error_tests': 0,
                'skipped_tests': 0
            },
            'recommendations': []
        }
        
        self.test_suites = [
            ('数据验证测试', TestDataValidation),
            ('数据加载器测试', TestDataLoader),
            ('融合模型测试', TestFusionModel),
            ('预警融合测试', TestWarningFusion),
            ('算法逻辑测试', TestAlgorithmLogic),
            ('算法准确性测试', TestAlgorithmAccuracy)
        ]
    
    def run_all_tests(self):
        """运行所有测试套件"""
        print("=" * 80)
        print("融合算法综合测试")
        print("=" * 80)
        
        self.results['start_time'] = datetime.now()
        
        for suite_name, test_class in self.test_suites:
            print(f"\n{'='*20} {suite_name} {'='*20}")
            
            suite_result = self._run_test_suite(suite_name, test_class)
            self.results['test_suites'][suite_name] = suite_result
            
            # 更新总计
            self.results['summary']['total_tests'] += suite_result['total']
            self.results['summary']['passed_tests'] += suite_result['passed']
            self.results['summary']['failed_tests'] += suite_result['failed']
            self.results['summary']['error_tests'] += suite_result['errors']
            self.results['summary']['skipped_tests'] += suite_result['skipped']
        
        self.results['end_time'] = datetime.now()
        self.results['total_duration'] = (
            self.results['end_time'] - self.results['start_time']
        ).total_seconds()
        
        self._generate_recommendations()
        self._print_summary()
        self._save_detailed_report()
    
    def _run_test_suite(self, suite_name, test_class):
        """运行单个测试套件"""
        suite_result = {
            'start_time': datetime.now(),
            'end_time': None,
            'duration': 0,
            'total': 0,
            'passed': 0,
            'failed': 0,
            'errors': 0,
            'skipped': 0,
            'test_details': []
        }
        
        try:
            # 创建测试套件
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromTestCase(test_class)
            
            # 运行测试
            stream = unittest.StringIO()
            runner = unittest.TextTestRunner(stream=stream, verbosity=2)
            result = runner.run(suite)
            
            # 收集结果
            suite_result['total'] = result.testsRun
            suite_result['passed'] = result.testsRun - len(result.failures) - len(result.errors) - len(result.skipped)
            suite_result['failed'] = len(result.failures)
            suite_result['errors'] = len(result.errors)
            suite_result['skipped'] = len(result.skipped)
            
            # 收集详细信息
            for test, error in result.failures:
                suite_result['test_details'].append({
                    'test_name': str(test),
                    'status': 'FAILED',
                    'message': error
                })
            
            for test, error in result.errors:
                suite_result['test_details'].append({
                    'test_name': str(test),
                    'status': 'ERROR',
                    'message': error
                })
            
            for test, reason in result.skipped:
                suite_result['test_details'].append({
                    'test_name': str(test),
                    'status': 'SKIPPED',
                    'message': reason
                })
            
            # 打印测试输出
            output = stream.getvalue()
            if output:
                print(output)
            
        except Exception as e:
            print(f"测试套件 {suite_name} 运行失败: {e}")
            traceback.print_exc()
            suite_result['errors'] = 1
            suite_result['test_details'].append({
                'test_name': suite_name,
                'status': 'ERROR',
                'message': str(e)
            })
        
        suite_result['end_time'] = datetime.now()
        suite_result['duration'] = (
            suite_result['end_time'] - suite_result['start_time']
        ).total_seconds()
        
        return suite_result
    
    def _generate_recommendations(self):
        """生成测试建议"""
        summary = self.results['summary']
        
        if summary['failed_tests'] > 0:
            self.results['recommendations'].append(
                f"发现 {summary['failed_tests']} 个测试失败，建议检查算法实现逻辑"
            )
        
        if summary['error_tests'] > 0:
            self.results['recommendations'].append(
                f"发现 {summary['error_tests']} 个测试错误，建议检查环境配置和依赖"
            )
        
        if summary['skipped_tests'] > 0:
            self.results['recommendations'].append(
                f"跳过了 {summary['skipped_tests']} 个测试，可能缺少测试数据或环境"
            )
        
        # 检查数据验证结果
        data_validation_suite = self.results['test_suites'].get('数据验证测试', {})
        if data_validation_suite.get('failed', 0) > 0 or data_validation_suite.get('errors', 0) > 0:
            self.results['recommendations'].append(
                "数据验证测试未通过，建议先解决数据质量问题再进行算法测试"
            )
        
        # 检查算法逻辑结果
        logic_suite = self.results['test_suites'].get('算法逻辑测试', {})
        if logic_suite.get('failed', 0) > 0:
            self.results['recommendations'].append(
                "算法逻辑测试未通过，建议检查模型架构和损失函数实现"
            )
        
        # 成功情况的建议
        if summary['failed_tests'] == 0 and summary['error_tests'] == 0:
            self.results['recommendations'].append(
                "所有核心测试通过，算法实现基本正确，可以进行进一步的性能优化"
            )
    
    def _print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 80)
        print("测试结果摘要")
        print("=" * 80)
        
        summary = self.results['summary']
        
        print(f"总测试时间: {self.results['total_duration']:.2f} 秒")
        print(f"总测试数量: {summary['total_tests']}")
        print(f"通过测试: {summary['passed_tests']} ✓")
        print(f"失败测试: {summary['failed_tests']} ✗")
        print(f"错误测试: {summary['error_tests']} ⚠")
        print(f"跳过测试: {summary['skipped_tests']} -")
        
        if summary['total_tests'] > 0:
            success_rate = (summary['passed_tests'] / summary['total_tests']) * 100
            print(f"成功率: {success_rate:.1f}%")
        
        print("\n各测试套件结果:")
        print("-" * 50)
        
        for suite_name, suite_result in self.results['test_suites'].items():
            status_icon = "✓" if suite_result['failed'] == 0 and suite_result['errors'] == 0 else "✗"
            print(f"{status_icon} {suite_name}: "
                  f"{suite_result['passed']}/{suite_result['total']} 通过 "
                  f"({suite_result['duration']:.2f}s)")
        
        print("\n建议:")
        print("-" * 50)
        for i, recommendation in enumerate(self.results['recommendations'], 1):
            print(f"{i}. {recommendation}")
    
    def _save_detailed_report(self):
        """保存详细测试报告"""
        report_dir = Path(__file__).parent / "reports"
        report_dir.mkdir(exist_ok=True)
        
        timestamp = self.results['start_time'].strftime('%Y%m%d_%H%M%S')
        
        # 保存JSON格式的详细报告
        json_report_path = report_dir / f"test_report_{timestamp}.json"
        
        # 转换datetime对象为字符串以便JSON序列化
        json_results = self._prepare_json_results()
        
        with open(json_report_path, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, ensure_ascii=False, indent=2)
        
        # 保存文本格式的摘要报告
        txt_report_path = report_dir / f"test_summary_{timestamp}.txt"
        
        with open(txt_report_path, 'w', encoding='utf-8') as f:
            f.write("融合算法测试报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"测试时间: {self.results['start_time'].strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试持续时间: {self.results['total_duration']:.2f} 秒\n\n")
            
            f.write("测试结果摘要:\n")
            f.write("-" * 30 + "\n")
            summary = self.results['summary']
            f.write(f"总测试数量: {summary['total_tests']}\n")
            f.write(f"通过: {summary['passed_tests']}\n")
            f.write(f"失败: {summary['failed_tests']}\n")
            f.write(f"错误: {summary['error_tests']}\n")
            f.write(f"跳过: {summary['skipped_tests']}\n")
            
            if summary['total_tests'] > 0:
                success_rate = (summary['passed_tests'] / summary['total_tests']) * 100
                f.write(f"成功率: {success_rate:.1f}%\n")
            
            f.write("\n各测试套件详情:\n")
            f.write("-" * 30 + "\n")
            
            for suite_name, suite_result in self.results['test_suites'].items():
                f.write(f"\n{suite_name}:\n")
                f.write(f"  总计: {suite_result['total']}\n")
                f.write(f"  通过: {suite_result['passed']}\n")
                f.write(f"  失败: {suite_result['failed']}\n")
                f.write(f"  错误: {suite_result['errors']}\n")
                f.write(f"  跳过: {suite_result['skipped']}\n")
                f.write(f"  耗时: {suite_result['duration']:.2f}s\n")
                
                if suite_result['test_details']:
                    f.write("  详细信息:\n")
                    for detail in suite_result['test_details']:
                        if detail['status'] in ['FAILED', 'ERROR']:
                            f.write(f"    {detail['status']}: {detail['test_name']}\n")
            
            f.write("\n建议:\n")
            f.write("-" * 30 + "\n")
            for i, recommendation in enumerate(self.results['recommendations'], 1):
                f.write(f"{i}. {recommendation}\n")
        
        print(f"\n详细报告已保存:")
        print(f"  JSON报告: {json_report_path}")
        print(f"  文本摘要: {txt_report_path}")
    
    def _prepare_json_results(self):
        """准备JSON序列化的结果"""
        json_results = {}
        
        for key, value in self.results.items():
            if isinstance(value, datetime):
                json_results[key] = value.isoformat()
            elif key == 'test_suites':
                json_results[key] = {}
                for suite_name, suite_result in value.items():
                    json_suite = {}
                    for suite_key, suite_value in suite_result.items():
                        if isinstance(suite_value, datetime):
                            json_suite[suite_key] = suite_value.isoformat()
                        else:
                            json_suite[suite_key] = suite_value
                    json_results[key][suite_name] = json_suite
            else:
                json_results[key] = value
        
        return json_results


def main():
    """主函数"""
    runner = ComprehensiveTestRunner()
    runner.run_all_tests()


if __name__ == '__main__':
    main()
