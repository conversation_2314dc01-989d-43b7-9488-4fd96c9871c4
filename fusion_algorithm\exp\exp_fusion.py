import os
import sys
import time
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch import optim
from torch.utils.data import DataLoader
import torch.nn.functional as F
from sklearn.metrics import precision_score, recall_score, f1_score, roc_curve, auc, accuracy_score
from pathlib import Path

# 添加项目根路径到sys.path，确保能找到所有模块
current_dir = Path(__file__).parent
project_root = current_dir.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 确保当前工作目录也在路径中
import os
current_work_dir = os.getcwd()
if current_work_dir not in sys.path:
    sys.path.insert(0, current_work_dir)

# 现在可以安全地导入项目模块
try:
    from data_provider.fusion_data_loader import FusionDataLoader
except ImportError as e:
    print(f"Warning: Could not import data_provider.fusion_data_loader: {e}")
    # 使用importlib动态导入
    import importlib.util
    data_loader_path = project_root / "data_provider" / "fusion_data_loader.py"
    spec = importlib.util.spec_from_file_location("fusion_data_loader", data_loader_path)
    data_loader_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(data_loader_module)
    FusionDataLoader = data_loader_module.FusionDataLoader
    print("✓ 使用importlib成功导入FusionDataLoader")

try:
    from models.fusion_model import FusionModel, FusionLoss
except ImportError as e:
    print(f"Warning: Could not import models.fusion_model: {e}")
    # 使用importlib动态导入
    import importlib.util
    fusion_model_path = project_root / "models" / "fusion_model.py"
    spec = importlib.util.spec_from_file_location("fusion_model", fusion_model_path)
    fusion_model_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(fusion_model_module)
    FusionModel = fusion_model_module.FusionModel
    FusionLoss = fusion_model_module.FusionLoss
    print("✓ 使用importlib成功导入FusionModel")

try:
    from utils.tools import EarlyStopping, adjust_learning_rate
    from utils.metrics import metric
    from utils.warning_fusion import WarningFusion
except ImportError as e:
    print(f"Warning: Could not import utils modules: {e}")
    EarlyStopping = None
    adjust_learning_rate = None
    metric = None
    WarningFusion = None

warnings.filterwarnings('ignore')


class Exp_Fusion:
    """融合实验类"""
    
    def __init__(self, args):
        self.args = args
        self.device = self._acquire_device()
        self.model = self._build_model().to(self.device)
        self.criterion = FusionLoss()
        
    def _acquire_device(self):
        if self.args.use_gpu:
            device = torch.device('cuda:{}'.format(self.args.gpu))
            print('Use GPU: cuda:{}'.format(self.args.gpu))
        else:
            device = torch.device('cpu')
            print('Use CPU')
        return device
    
    def _build_model(self):
        model = FusionModel(self.args)
        return model
    
    def _get_data(self, flag, task_type="fusion"):
        data_set = FusionDataLoader(
            root_path=self.args.root_path,
            win_size=self.args.seq_len,
            step=1,
            flag=flag,
            task_type=task_type
        )
        
        batch_size = self.args.batch_size
        if flag == 'test':
            batch_size = 1
        
        data_loader = DataLoader(
            data_set,
            batch_size=batch_size,
            shuffle=(flag == 'train'),
            num_workers=self.args.num_workers,
            drop_last=(flag == 'train')
        )
        
        return data_set, data_loader
    
    def _select_optimizer(self):
        model_optim = optim.Adam(self.model.parameters(), lr=self.args.learning_rate)
        return model_optim
    
    def train_anomaly_detection(self, setting):
        """训练异常检测分支"""
        print("Training Anomaly Detection Branch...")
        
        train_data, train_loader = self._get_data(flag='train', task_type='anomaly_detection')
        vali_data, vali_loader = self._get_data(flag='val', task_type='anomaly_detection')
        
        path = os.path.join(self.args.checkpoints, setting)
        if not os.path.exists(path):
            os.makedirs(path)
        
        early_stopping = EarlyStopping(patience=self.args.patience, verbose=True)
        model_optim = self._select_optimizer()
        
        for epoch in range(self.args.train_epochs):
            train_loss = []
            self.model.train()
            epoch_time = time.time()
            
            for i, (batch_x, batch_y, timestamps) in enumerate(train_loader):
                model_optim.zero_grad()
                batch_x = batch_x.float().to(self.device)
                
                outputs = self.model(batch_x, task_mode="anomaly")
                loss = self.criterion(outputs, batch_x, task_mode="anomaly")
                
                train_loss.append(loss.item())
                loss.backward()
                model_optim.step()
                
                if (i + 1) % 100 == 0:
                    print(f"\tEpoch: {epoch+1}, Iter: {i+1}, Loss: {loss.item():.7f}")
            
            print(f"Epoch: {epoch+1} cost time: {time.time() - epoch_time}")
            train_loss = np.average(train_loss)
            
            vali_loss = self.vali_anomaly(vali_data, vali_loader)
            print(f"Epoch: {epoch+1}, Train Loss: {train_loss:.3f}, Vali Loss: {vali_loss:.3f}")
            
            early_stopping(vali_loss, self.model, path)
            if early_stopping.early_stop:
                print("Early stopping")
                break
            
            adjust_learning_rate(model_optim, epoch + 1, self.args)
    
    def train_earlysignal_detection(self, setting):
        """训练前驱信号检测分支"""
        print("Training Early Signal Detection Branch...")
        
        train_data, train_loader = self._get_data(flag='earlysignal', task_type='earlysignaldet')
        
        path = os.path.join(self.args.checkpoints, setting)
        if not os.path.exists(path):
            os.makedirs(path)
        
        model_optim = self._select_optimizer()
        
        for epoch in range(self.args.train_epochs):
            train_loss = []
            self.model.train()
            epoch_time = time.time()
            
            for i, (batch_x, batch_y, file_path) in enumerate(train_loader):
                model_optim.zero_grad()
                batch_x = batch_x.float().to(self.device)
                batch_y = batch_y.long().to(self.device)
                
                outputs = self.model(batch_x, task_mode="earlysignal")
                loss = self.criterion(outputs, batch_y, task_mode="earlysignal")
                
                train_loss.append(loss.item())
                loss.backward()
                model_optim.step()
                
                if (i + 1) % 100 == 0:
                    print(f"\tEpoch: {epoch+1}, Iter: {i+1}, Loss: {loss.item():.7f}")
            
            print(f"Epoch: {epoch+1} cost time: {time.time() - epoch_time}")
            train_loss = np.average(train_loss)
            print(f"Epoch: {epoch+1}, Train Loss: {train_loss:.3f}")
            
            adjust_learning_rate(model_optim, epoch + 1, self.args)
    
    def vali_anomaly(self, vali_data, vali_loader):
        """验证异常检测"""
        total_loss = []
        self.model.eval()
        
        with torch.no_grad():
            for i, (batch_x, batch_y, timestamps) in enumerate(vali_loader):
                batch_x = batch_x.float().to(self.device)
                
                outputs = self.model(batch_x, task_mode="anomaly")
                loss = self.criterion(outputs, batch_x, task_mode="anomaly")
                total_loss.append(loss.item())
        
        total_loss = np.average(total_loss)
        self.model.train()
        return total_loss
    
    def test_anomaly_detection(self, setting, test=0):
        """测试异常检测"""
        print("Testing Anomaly Detection...")
        
        test_data, test_loader = self._get_data(flag='test', task_type='anomaly_detection')
        
        if test:
            print('Loading model from checkpoint...')
            self.model.load_state_dict(torch.load(os.path.join('./checkpoints/' + setting, 'checkpoint.pth')))
        
        attens_energy = []
        self.model.eval()
        
        with torch.no_grad():
            for i, (batch_x, batch_y, timestamps) in enumerate(test_loader):
                batch_x = batch_x.float().to(self.device)
                outputs = self.model(batch_x, task_mode="anomaly")
                
                # 计算重构误差
                score = torch.mean((outputs - batch_x) ** 2, dim=-1)
                score = score.detach().cpu().numpy()
                attens_energy.append(score)
        
        attens_energy = np.concatenate(attens_energy, axis=0).reshape(-1)
        test_energy = np.array(attens_energy)
        
        # 使用训练数据确定阈值
        train_data, train_loader = self._get_data(flag='train', task_type='anomaly_detection')
        train_energy = []
        
        with torch.no_grad():
            for i, (batch_x, batch_y, timestamps) in enumerate(train_loader):
                batch_x = batch_x.float().to(self.device)
                outputs = self.model(batch_x, task_mode="anomaly")
                score = torch.mean((outputs - batch_x) ** 2, dim=-1)
                score = score.detach().cpu().numpy()
                train_energy.append(score)
        
        train_energy = np.concatenate(train_energy, axis=0).reshape(-1)
        combined_energy = np.concatenate([train_energy, test_energy], axis=0)
        threshold = np.percentile(combined_energy, 100 - self.args.anomaly_ratio)
        
        print(f"Anomaly Detection Threshold: {threshold}")
        
        # 预测和评估
        pred = (test_energy > threshold).astype(int)
        
        # 保存结果
        folder_path = './results/' + setting + '/'
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)
        
        np.save(folder_path + 'anomaly_scores.npy', test_energy)
        np.save(folder_path + 'anomaly_pred.npy', pred)
        
        return test_energy, pred, threshold
    
    def test_earlysignal_detection(self, setting, test=0):
        """测试前驱信号检测"""
        print("Testing Early Signal Detection...")
        
        test_data, test_loader = self._get_data(flag='earlysignal', task_type='earlysignaldet')
        
        if test:
            print('Loading model from checkpoint...')
            self.model.load_state_dict(torch.load(os.path.join('./checkpoints/' + setting, 'checkpoint.pth')))
        
        preds = []
        trues = []
        probs = []
        filenames = []
        
        self.model.eval()
        with torch.no_grad():
            for i, (batch_x, batch_y, file_path) in enumerate(test_loader):
                batch_x = batch_x.float().to(self.device)
                batch_y = batch_y.long().to(self.device)
                
                outputs = self.model(batch_x, task_mode="earlysignal")
                prob = F.softmax(outputs, dim=1)
                
                preds.append(outputs.detach())
                trues.append(batch_y)
                probs.extend(prob.cpu().numpy())
                filenames.extend(file_path)
        
        preds = torch.cat(preds, 0)
        trues = torch.cat(trues, 0)
        
        # 计算指标
        probs_array = np.array(probs)
        predictions = np.argmax(probs_array, axis=1)
        trues_array = trues.flatten().cpu().numpy()
        
        accuracy = accuracy_score(trues_array, predictions)
        precision = precision_score(trues_array, predictions, average='weighted')
        recall = recall_score(trues_array, predictions, average='weighted')
        f1 = f1_score(trues_array, predictions, average='weighted')
        
        # ROC-AUC
        if len(np.unique(trues_array)) > 1:
            fpr, tpr, _ = roc_curve(trues_array, probs_array[:, 1])
            roc_auc = auc(fpr, tpr)
        else:
            roc_auc = 0.0
        
        print(f'Early Signal Detection Results:')
        print(f'Accuracy: {accuracy:.4f}')
        print(f'Precision: {precision:.4f}')
        print(f'Recall: {recall:.4f}')
        print(f'F1-Score: {f1:.4f}')
        print(f'ROC-AUC: {roc_auc:.4f}')
        
        # 保存结果
        folder_path = './results/' + setting + '/'
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)
        
        results_df = pd.DataFrame({
            'Filename': filenames,
            'Risk': probs_array[:, 1],
            'Predicted_Label': predictions,
            'GroundTruth': trues_array
        })
        results_df.to_csv(folder_path + 'earlysignal_predictions.csv', index=False, encoding='gbk')
        
        return accuracy, precision, recall, f1, roc_auc
    
    def train(self, setting):
        """完整训练流程"""
        print("Starting Fusion Model Training...")
        
        # 分别训练两个分支
        self.train_anomaly_detection(setting + '_anomaly')
        self.train_earlysignal_detection(setting + '_earlysignal')
        
        print("Fusion Model Training Completed!")
    
    def test_fusion_warnings(self, setting, test=0):
        """测试融合预警功能"""
        print("Testing Fusion Warning System...")

        # 分别测试两个分支并获取结果
        anomaly_results = self.test_anomaly_detection(setting + '_anomaly', test)
        earlysignal_results = self.test_earlysignal_detection(setting + '_earlysignal', test)

        # 准备异常检测结果数据
        test_data, test_loader = self._get_data(flag='test', task_type='anomaly_detection')
        anomaly_scores, anomaly_pred, threshold = anomaly_results

        # 创建异常检测结果DataFrame
        anomaly_df = pd.DataFrame({
            'Timestamp': test_data.anomaly_test_timestamps[:len(anomaly_scores)],
            'Anomaly_Score': anomaly_scores,
            'Predicted_Anomaly': anomaly_pred
        })

        # 准备前驱信号检测结果数据
        earlysignal_data, earlysignal_loader = self._get_data(flag='earlysignal', task_type='earlysignaldet')
        accuracy, precision, recall, f1, roc_auc = earlysignal_results

        # 读取前驱信号检测的详细结果
        results_path = f'./results/{setting}_earlysignal/earlysignal_predictions.csv'
        if os.path.exists(results_path):
            earlysignal_df = pd.read_csv(results_path, encoding='gbk')
        else:
            print(f"Warning: 前驱信号检测结果文件不存在: {results_path}")
            earlysignal_df = pd.DataFrame(columns=['Filename', 'Risk', 'Predicted_Label'])

        # 创建预警融合器
        warning_fusion = WarningFusion(
            min_duration_minutes=getattr(self.args, 'min_warning_duration', 1.0),
            merge_gap_minutes=getattr(self.args, 'merge_gap_minutes', 5.0)
        )

        # 执行预警融合
        fused_warnings = warning_fusion.fuse_warnings(
            anomaly_df,
            earlysignal_df,
            anomaly_min_duration=getattr(self.args, 'anomaly_min_duration', 5.0),
            earlysignal_min_risk=getattr(self.args, 'earlysignal_min_risk', 0.7)
        )

        # 保存融合结果
        folder_path = './results/' + setting + '/'
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)

        # 导出预警时间段
        warning_fusion.export_warnings_to_csv(
            fused_warnings,
            folder_path + 'fused_warnings.csv'
        )

        # 生成融合报告
        self._generate_fusion_report(fused_warnings, anomaly_results, earlysignal_results, folder_path)

        print("Fusion Warning System Testing Completed!")

        return {
            'anomaly': anomaly_results,
            'earlysignal': earlysignal_results,
            'fused_warnings': fused_warnings,
            'warning_count': len(fused_warnings)
        }

    def _generate_fusion_report(self, fused_warnings, anomaly_results, earlysignal_results, output_path):
        """生成融合报告"""
        report_path = output_path + 'fusion_report.txt'

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("融合算法预警报告\n")
            f.write("=" * 60 + "\n\n")

            # 异常检测结果
            f.write("异常检测结果:\n")
            f.write("-" * 30 + "\n")
            anomaly_scores, anomaly_pred, threshold = anomaly_results
            f.write(f"异常阈值: {threshold:.4f}\n")
            f.write(f"检测到异常点数: {np.sum(anomaly_pred)}\n")
            f.write(f"异常比例: {np.sum(anomaly_pred)/len(anomaly_pred)*100:.2f}%\n\n")

            # 前驱信号检测结果
            f.write("前驱信号检测结果:\n")
            f.write("-" * 30 + "\n")
            accuracy, precision, recall, f1, roc_auc = earlysignal_results
            f.write(f"准确率: {accuracy:.4f}\n")
            f.write(f"精确率: {precision:.4f}\n")
            f.write(f"召回率: {recall:.4f}\n")
            f.write(f"F1分数: {f1:.4f}\n")
            f.write(f"ROC-AUC: {roc_auc:.4f}\n\n")

            # 融合预警结果
            f.write("融合预警结果:\n")
            f.write("-" * 30 + "\n")
            f.write(f"总预警时间段数: {len(fused_warnings)}\n")

            if fused_warnings:
                total_duration = sum(w.duration_minutes for w in fused_warnings)
                f.write(f"总预警时长: {total_duration:.2f} 分钟\n")
                f.write(f"平均预警时长: {total_duration/len(fused_warnings):.2f} 分钟\n")

                # 按类型统计
                type_counts = {}
                for warning in fused_warnings:
                    type_counts[warning.warning_type] = type_counts.get(warning.warning_type, 0) + 1

                f.write("\n按类型统计:\n")
                for warning_type, count in type_counts.items():
                    f.write(f"  {warning_type}: {count} 个\n")

                f.write("\n详细预警时间段:\n")
                for i, warning in enumerate(fused_warnings, 1):
                    f.write(f"{i}. {warning.start_time} - {warning.end_time} "
                           f"({warning.duration_minutes:.2f}分钟, {warning.warning_type}, "
                           f"置信度:{warning.confidence:.3f})\n")
            else:
                f.write("未检测到预警时间段\n")

        print(f"融合报告已保存到: {report_path}")

    def test(self, setting, test=0):
        """完整测试流程"""
        print("Starting Fusion Model Testing...")

        # 执行融合预警测试
        results = self.test_fusion_warnings(setting, test)

        print("Fusion Model Testing Completed!")
        return results
