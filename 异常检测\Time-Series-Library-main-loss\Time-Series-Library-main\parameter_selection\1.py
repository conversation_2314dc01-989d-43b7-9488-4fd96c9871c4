import pandas as pd
import matplotlib.pyplot as plt

# 假设 rankCor 是你的结果列表
rankCor = [
    ('H2S3', 0.8379012183780121), ('RPM', 0.8359745226374583), ('NC4', 0.834996353627267),
    ('C3', 0.8332906171796701), ('NC5', 0.8331297797394523), ('PIT6', 0.832693307755964),
    ('H2S4', 0.8314578215651546), ('C2', 0.8314456676785664), ('PIT5', 0.8306251063272337),
    ('MCONDOUT', 0.8293757441258925), ('PIT4', 0.829132386920552), ('WOB', 0.8286838269231944),
    ('SIGMA', 0.82838796589285), ('IC5', 0.8283518561859167), ('PIT2', 0.8280613879343974),
    ('CO2', 0.8280175754755205), ('DEP', 0.8280030397196143), ('BITDIST', 0.8280030397196143),
    ('BITDEP', 0.8279770139260563), ('OVERFLOW', 0.8278832506225965), ('IC4', 0.827465097488065),
    ('MWIN', 0.8271402623209833), ('DRITIME', 0.8245005102214643), ('TGAS', 0.8241170833473287),
    ('C1', 0.8239798332403776), ('PIT1', 0.8214635555400562), ('PIT3', 0.818922292016205),
    ('MTOUT', 0.8185461919574467), ('PITTOT', 0.816146844946572), ('HOKHEI', 0.8143951385696377),
    ('PIT7', 0.8136435165045712), ('TOR', 0.8007779622065304), ('MTIN', 0.778428115249676),
    ('HKLD', 0.7749466995367472), ('MCONDIN', 0.7724699617725251), ('STRSUM', 0.7593010861058266),
    ('MWOUT', 0.7228834554298984), ('FLOWOUT', 0.7188609942663465), ('SPP', 0.7159191488533179),
    ('FLOWIN', 0.7123415643198087), ('STR2', 0.7070947451794015), ('STR1', 0.656978300889795)
]

# 转换为 DataFrame
df_rank = pd.DataFrame(rankCor, columns=['Feature', 'Correlation'])

# 绘制柱状图
plt.figure(figsize=(12, 8))
plt.barh(df_rank['Feature'], df_rank['Correlation'], color='skyblue')
plt.xlabel('Correlation')
plt.ylabel('Feature')
plt.title('Gray Relational Degree Ranking')
plt.gca().invert_yaxis()  # 反转 Y 轴，使最高相关性值在顶部
plt.show()
