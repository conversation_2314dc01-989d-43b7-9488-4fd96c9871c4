import os
import pandas as pd

# ====== 修改下面这两个路径 ======
input_folder = "D:\卡钻\最新数据/2024.6.1430口井卡钻预警测试数据\新建文件夹"  # 输入：包含多个井的 CSV 文件的文件夹路径
output_root = "D:\卡钻\最新数据/2024.6.1430口井卡钻预警测试数据\处理后的数据"  # 输出：拆分后保存到哪个文件夹
# ==================================

segment_duration = pd.Timedelta(minutes=3)

# 期望的列顺序
columns_order = ['date', 'DEP', 'BITDEP', 'HOKHEI', 'DRITIME',
                 'WOB', 'HKLD', 'RPM', 'TOR', 'FLOWIN', 'FLOWOUT', 'SPP']

# 确保输出目录存在
os.makedirs(output_root, exist_ok=True)

# 遍历每个CSV文件
for file_name in os.listdir(input_folder):
    if not file_name.lower().endswith(".csv"):
        continue

    file_path = os.path.join(input_folder, file_name)
    well_name = os.path.splitext(file_name)[0]  # 获取井的名称（文件名）

    try:
        df = pd.read_csv(file_path)
        df = df[columns_order]
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values(by='date')

        # 设定时间范围
        start_time = df['date'].min()
        end_time = df['date'].max()
        current_start = start_time
        segment_num = 1

        # 创建保存文件夹
        well_output_dir = os.path.join(output_root, f"{well_name}_segments")
        os.makedirs(well_output_dir, exist_ok=True)

        # 开始拆分
        while current_start <= end_time:
            current_end = current_start + segment_duration
            segment_df = df[(df['date'] >= current_start) & (df['date'] < current_end)]

            if not segment_df.empty:
                # 格式化时间并加入文件名，包含井号和时间戳
                time_str = current_start.strftime("%Y-%m-%d_%H-%M-%S")
                segment_file = os.path.join(well_output_dir, f"{well_name}_{time_str}_segment{segment_num}.csv")
                segment_df.to_csv(segment_file, index=False)
                segment_num += 1

            current_start = current_end

        print(f"✅ {well_name} 拆分完成，共生成 {segment_num - 1} 个片段")

    except Exception as e:
        print(f"❌ 处理 {well_name} 时出错：{e}")
