# 融合算法训练指南

## 🎯 快速开始

您的融合算法已经完全可用！以下是开始实际数据训练的步骤：

### 1. 环境准备

确保您已激活正确的conda环境：
```bash
conda activate LTS2
```

### 2. 验证环境

首先运行快速验证确保一切正常：
```bash
cd fusion_algorithm/tests
python quick_validation.py
```

应该看到 **100% 成功率** 的结果。

### 3. 开始训练

有三种训练方式可选：

#### 方式一：简单启动训练（推荐）
```bash
cd fusion_algorithm
python start_training.py
```

#### 方式二：使用训练脚本
```bash
cd fusion_algorithm/scripts
python train_with_real_data.py --mode both --train_epochs 20
```

#### 方式三：使用原始运行脚本
```bash
cd fusion_algorithm
python run_fusion.py --is_training 1 --task_name fusion
```

## 📊 训练模式说明

### 异常检测训练
- **数据来源**: `异常检测/Time-Series-Library-main-loss/Time-Series-Library-main/dataset/`
- **任务类型**: 时间序列重构
- **输出**: 重构后的时间序列
- **损失函数**: MSE损失

### 前驱信号检测训练
- **数据来源**: `前驱信号检测/dataset/`
- **任务类型**: 二分类（正常/前驱）
- **输出**: 分类概率
- **损失函数**: 交叉熵损失

### 融合训练
- **策略**: 先分别训练两个分支，再联合微调
- **优势**: 充分利用两种数据的特性
- **结果**: 统一的预警融合系统

## ⚙️ 配置参数

### 关键参数说明
```python
seq_len = 96          # 输入序列长度
d_model = 128         # 模型维度
batch_size = 8        # 批次大小（已优化内存使用）
learning_rate = 0.0001 # 学习率
train_epochs = 20     # 训练轮数
patience = 5          # 早停耐心值
```

### 数据路径配置
- 异常检测数据: `../异常检测/Time-Series-Library-main-loss/Time-Series-Library-main/dataset/`
- 前驱信号数据: `../前驱信号检测/dataset/`

## 📈 训练过程监控

### 训练日志
训练过程中会显示：
- 每个epoch的损失值
- 验证集性能
- 早停信息
- 模型保存信息

### 输出文件
- **模型文件**: `checkpoints/` 目录
- **训练日志**: `results/` 目录
- **性能指标**: 控制台输出

## 🔧 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 减少批次大小
   python start_training.py  # 已设置batch_size=8
   ```

2. **CUDA错误**
   ```bash
   # 强制使用CPU
   export CUDA_VISIBLE_DEVICES=""
   python start_training.py
   ```

3. **数据加载失败**
   ```bash
   # 检查数据路径
   python tests/quick_validation.py
   ```

### 调试建议

1. **逐步验证**:
   - 先运行 `quick_validation.py`
   - 再运行 `start_training.py`
   - 最后尝试完整训练

2. **监控资源**:
   - 观察内存使用情况
   - 监控GPU利用率
   - 检查磁盘空间

3. **参数调优**:
   - 从小批次开始
   - 逐步增加训练轮数
   - 根据验证集调整学习率

## 📋 训练检查清单

训练前请确认：

- [ ] 环境已激活 (`conda activate LTS2`)
- [ ] 快速验证通过 (100% 成功率)
- [ ] 数据路径存在且可访问
- [ ] 有足够的磁盘空间保存模型
- [ ] GPU内存充足（如使用GPU）

## 🎯 预期结果

### 训练成功标志
- 异常检测损失逐步下降
- 前驱信号分类准确率提升
- 模型文件成功保存
- 无内存或CUDA错误

### 性能指标
- **异常检测**: 重构误差应逐步降低
- **前驱信号**: 分类准确率应达到70%+
- **训练时间**: 每个epoch约1-5分钟（取决于硬件）

## 🚀 下一步

训练完成后，您可以：

1. **评估模型性能**
   ```bash
   python tests/run_comprehensive_tests.py
   ```

2. **进行推理测试**
   ```bash
   python run_fusion.py --is_training 0
   ```

3. **调优超参数**
   - 调整学习率
   - 修改模型架构
   - 优化训练策略

4. **部署应用**
   - 集成到生产系统
   - 建立监控机制
   - 设置预警阈值

## 💡 提示

- 首次训练建议使用较少的epoch数（如20）进行测试
- 观察损失曲线判断是否需要调整参数
- 保存训练日志以便后续分析
- 定期备份重要的模型检查点

---

**准备好了吗？** 运行 `python start_training.py` 开始您的融合算法训练之旅！
