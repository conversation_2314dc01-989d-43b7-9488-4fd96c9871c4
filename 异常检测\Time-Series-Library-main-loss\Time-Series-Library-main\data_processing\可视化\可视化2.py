import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 读取CSV文件
df = pd.read_csv('宁215实时数据.csv')

# 将时间列转换为datetime类型，并设置为索引
df['date'] = pd.to_datetime(df['date'])
df.set_index('date', inplace=True)

# 获取特征列名（排除sfkz）
features = ['TVD Depth', 'Bit Depth', 'RPM', 'SPP', 'TORQUE',  'ROP', 'WOB']

# 手动指定每个特征的颜色
colors = [
    'blue', 'green', 'red', 'purple', 'orange',
    'brown', 'pink'
]

def highlight_long_segments(index, mask, min_length=60):
    """高亮显示连续长度超过min_length的区域"""
    segments = []
    start = None
    for i, val in enumerate(mask):
        if val and start is None:
            start = i
        elif not val and start is not None:
            if i - start >= min_length:
                segments.append((start, i))
            start = None
    if start is not None and len(mask) - start >= min_length:
        segments.append((start, len(mask)))
    return segments

# 创建子图
fig, axes = plt.subplots(nrows=len(features), ncols=1, figsize=(12, 24), sharex=True)

# 存储已打印的时间段
printed_segments = set()

# 绘制每个特征的折线图
for i, (feature, color) in enumerate(zip(features, colors)):
    ax = axes[i]
    ax.plot(df.index, df[feature], label=feature, color=color)  # 使用指定颜色绘制折线图

    # 标记特征sfkz为1的值，使用透明的黄色表示
    sfkz_mask = df['sfkz'] == 1
    ax.fill_between(df.index, df[feature].min(), df[feature].max(), where=sfkz_mask, color='yellow', alpha=0.3)

    # 高亮显示连续长度超过60的区域
    segments = highlight_long_segments(df.index, sfkz_mask.values, min_length=60)  # 使用sfkz_mask.values来确保是一个numpy数组
    for start, end in segments:
        # 高亮显示区域
        ax.axvspan(df.index[start], df.index[end - 1], color='red', alpha=0.2)  # 调整红色的透明度为0.2

        # 格式化时间段
        segment_str = f'{df.index[start]} to {df.index[end - 1]}'
        if segment_str not in printed_segments:
            printed_segments.add(segment_str)
            print(f'高风险 from {df.index[start]} to {df.index[end - 1]}')

    ax.set_ylabel(feature)
    ax.legend()

# 设置整体标题和横坐标标签
plt.suptitle('Time Series of Features')
plt.xlabel('Date')

# 调整布局，避免重叠
plt.tight_layout(rect=[0, 0, 1, 0.95])

# 显示图形
plt.show()

# import pandas as pd
# import matplotlib.pyplot as plt
# import numpy as np
#
# # 读取CSV文件
# df = pd.read_csv('泸208H3-6_正常.csv')
#
# # 将时间列转换为datetime类型，并设置为索引
# df['date'] = pd.to_datetime(df['date'])
# df.set_index('date', inplace=True)
#
# # 获取特征列名（排除sfkz）
# features = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']
#
# # 手动指定每个特征的颜色
# colors = [
#     'blue', 'green', 'red', 'purple', 'orange',
#     'brown', 'pink', 'gray', 'cyan', 'magenta'
# ]
#
#
# def highlight_long_segments(index, mask, min_length=60):
#     """高亮显示连续长度超过min_length的区域"""
#     segments = []
#     start = None
#     for i, val in enumerate(mask):
#         if val and start is None:
#             start = i
#         elif not val and start is not None:
#             if i - start >= min_length:
#                 segments.append((start, i))
#             start = None
#     if start is not None and len(mask) - start >= min_length:
#         segments.append((start, len(mask)))
#     return segments
#
#
# # 创建子图
# fig, axes = plt.subplots(nrows=len(features), ncols=1, figsize=(12, 24), sharex=True)
#
# # 存储已打印的时间段
# printed_segments = set()
#
# # 绘制每个特征的折线图
# for i, (feature, color) in enumerate(zip(features, colors)):
#     ax = axes[i]
#     ax.plot(df.index, df[feature], label=feature, color=color)  # 使用指定颜色绘制折线图
#
#     # 标记特征sfkz为1的值，使用透明的黄色表示
#     sfkz_mask = df['sfkz'] == 1
#     ax.fill_between(df.index, df[feature].min(), df[feature].max(), where=sfkz_mask, color='yellow', alpha=0.3)
#
#     # 高亮显示连续长度超过60的区域
#     segments = highlight_long_segments(df.index, sfkz_mask, min_length=60)
#     for start, end in segments:
#         # 高亮显示区域
#         ax.axvspan(df.index[start], df.index[end - 1], color='red', alpha=0.3)
#
#         # 格式化时间段
#         segment_str = f'{df.index[start]} to {df.index[end - 1]}'
#         if segment_str not in printed_segments:
#             printed_segments.add(segment_str)
#             print(f'高风险 from {df.index[start]} to {df.index[end - 1]}')
#
#     ax.set_ylabel(feature)
#     ax.legend()
#
# # 设置整体标题和横坐标标签
# plt.suptitle('Time Series of Features')
# plt.xlabel('Date')
#
# # 调整布局，避免重叠
# plt.tight_layout(rect=[0, 0, 1, 0.95])
#
# # 显示图形
# plt.show()
