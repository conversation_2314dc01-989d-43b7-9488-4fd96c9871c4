# 融合算法MVP - 前驱信号检测与异常检测

## 概述

这是一个融合前驱信号检测和异常检测的MVP（最小可行产品）实现。该算法基于统一的Transformer架构，能够同时处理两种不同的时间序列分析任务。

## 核心特性

- **统一架构**: 基于PatchTST的Transformer编码器
- **双分支设计**: 异常检测分支 + 前驱信号检测分支
- **灵活数据接口**: 支持CSV格式的数据输入
- **独立训练**: 两个分支可以分别训练，使用不同的数据集
- **预警融合**: 将两种算法的结果融合为统一的预警时间段
- **时间段分析**: 自动识别连续异常和高风险时间段
- **智能合并**: 合并重叠或相近的预警时间段

## 目录结构

```
fusion_algorithm/
├── configs/
│   └── fusion_config.py          # 配置文件
├── data_provider/
│   └── fusion_data_loader.py     # 统一数据加载器
├── exp/
│   └── exp_fusion.py             # 融合实验类
├── models/
│   └── fusion_model.py           # 融合模型架构
├── scripts/
│   └── run_fusion_mvp.py         # MVP启动脚本
├── run_fusion.py                 # 主运行脚本
└── README.md                     # 说明文档
```

## 数据格式要求

### 异常检测数据（CSV格式）
```
dataset/anomaly/
├── train/
│   ├── train_file1.csv           # 训练数据文件（包含date列和数值特征）
│   ├── train_file2.csv
│   └── ...
└── test_data.csv                 # 测试数据（包含date列和数值特征）
```

**注意**：异常检测是无监督学习，不需要测试标签文件。

**CSV格式要求**：
- 必须包含`date`列（时间戳，格式：YYYY-MM-DD HH:MM:SS）
- 其他列为数值特征（如：DEP, BITDEP, HOKHEI, WOB, RPM, TOR, SPP等）
- 编码格式：UTF-8或ANSI

### 前驱信号检测数据
```
dataset/earlysignal/
├── 正常数据/
│   ├── normal_sample1.csv        # 正常样本
│   ├── normal_sample2.csv
│   └── ...
└── 前驱/
    ├── precursor_sample1.csv     # 前驱样本
    ├── precursor_sample2.csv
    └── ...
```

**文件命名要求**：
- 格式：`井号_YYYY-MM-DD_HH-MM-SS.csv`
- 例如：`1井_2024-01-15_10-30-45.csv`
- 用于提取时间信息进行预警时间段分析

## 快速开始

### 1. 环境准备
确保安装了以下依赖：
- Python 3.7+
- PyTorch 1.8+
- NumPy
- Pandas
- Scikit-learn

### 2. 数据准备
按照上述格式组织您的数据文件。

### 3. 运行MVP
```bash
cd fusion_algorithm
python scripts/run_fusion_mvp.py
```

### 4. 直接运行
也可以直接使用命令行参数：

**训练模式:**
```bash
python run_fusion.py \
    --task_name fusion \
    --is_training 1 \
    --root_path ./dataset/ \
    --model_id fusion_mvp \
    --seq_len 96 \
    --d_model 128 \
    --batch_size 16 \
    --train_epochs 50
```

**测试模式:**
```bash
python run_fusion.py \
    --task_name fusion \
    --is_training 0 \
    --root_path ./dataset/ \
    --model_id fusion_mvp \
    --seq_len 96 \
    --d_model 128
```

## 模型架构

### 核心组件
1. **PatchEmbedding**: 将时间序列分割成patches
2. **TransformerEncoder**: 多层自注意力编码器
3. **AnomalyHead**: 异常检测重构头
4. **EarlySignalHead**: 前驱信号分类头

### 训练策略
- **分离训练**: 两个分支使用各自的数据集独立训练
- **统一测试**: 在相同的测试集上评估性能
- **损失函数**: MSE损失（异常检测）+ CrossEntropy损失（前驱信号检测）

## 输出结果

### 异常检测结果
- `anomaly_scores.npy`: 异常分数
- `anomaly_pred.npy`: 异常预测结果

### 前驱信号检测结果
- `earlysignal_predictions.csv`: 详细预测结果
  - Filename: 文件名
  - Risk: 风险概率
  - Predicted_Label: 预测标签
  - GroundTruth: 真实标签

### 融合预警结果
- `fused_warnings.csv`: 融合后的预警时间段
  - Warning_ID: 预警编号
  - Start_Time: 开始时间
  - End_Time: 结束时间
  - Duration_Minutes: 持续时间（分钟）
  - Warning_Type: 预警类型（anomaly/earlysignal/fusion）
  - Confidence: 置信度
  - Source: 数据源
  - Metadata: 元数据信息

- `fusion_report.txt`: 详细融合报告
  - 异常检测统计信息
  - 前驱信号检测性能指标
  - 预警时间段详细列表
  - 按类型统计的预警数量

## 配置参数

### 关键参数说明
- `seq_len`: 输入序列长度（默认96）
- `d_model`: 模型维度（默认128）
- `patch_len`: Patch长度（默认16）
- `stride`: Patch步长（默认8）
- `e_layers`: 编码器层数（默认3）
- `batch_size`: 批次大小（默认16）
- `anomaly_ratio`: 异常比例阈值（默认1.0）

## 扩展说明

### 添加新的数据源
1. 修改`FusionDataLoader`类
2. 添加新的数据加载逻辑
3. 更新配置文件

### 调整模型架构
1. 修改`FusionModel`类
2. 调整分支结构
3. 更新损失函数

### 性能优化
1. 调整batch_size和学习率
2. 使用混合精度训练
3. 添加数据增强

## 注意事项

1. **内存使用**: 大数据集可能需要调整batch_size
2. **GPU支持**: 自动检测GPU可用性
3. **数据格式**: 确保CSV文件编码为ANSI或UTF-8
4. **路径配置**: 检查数据路径是否正确

## 故障排除

### 常见问题
1. **数据加载失败**: 检查文件路径和格式
2. **内存不足**: 减少batch_size或序列长度
3. **训练不收敛**: 调整学习率或模型参数

### 调试建议
1. 使用小数据集进行初始测试
2. 检查数据预处理步骤
3. 监控训练损失变化

## 版本信息

- 版本: MVP 1.0
- 更新日期: 2024年
- 兼容性: PyTorch 1.8+, Python 3.7+
