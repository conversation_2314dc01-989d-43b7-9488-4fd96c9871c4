#!/usr/bin/env python3
"""
工具函数模块
提供训练过程中需要的辅助工具
"""

import numpy as np
import torch
import torch.nn as nn
import os


class EarlyStopping:
    """早停机制"""
    def __init__(self, patience=7, verbose=False, delta=0, path='checkpoint.pth', trace_func=print):
        self.patience = patience
        self.verbose = verbose
        self.counter = 0
        self.best_score = None
        self.early_stop = False
        self.val_loss_min = np.Inf
        self.delta = delta
        self.path = path
        self.trace_func = trace_func
    
    def __call__(self, val_loss, model):
        score = -val_loss
        
        if self.best_score is None:
            self.best_score = score
            self.save_checkpoint(val_loss, model)
        elif score < self.best_score + self.delta:
            self.counter += 1
            if self.verbose:
                self.trace_func(f'EarlyStopping counter: {self.counter} out of {self.patience}')
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_score = score
            self.save_checkpoint(val_loss, model)
            self.counter = 0
    
    def save_checkpoint(self, val_loss, model):
        """保存模型检查点"""
        if self.verbose:
            self.trace_func(f'Validation loss decreased ({self.val_loss_min:.6f} --> {val_loss:.6f}). Saving model ...')
        torch.save(model.state_dict(), self.path)
        self.val_loss_min = val_loss


def adjust_learning_rate(optimizer, epoch, args):
    """调整学习率"""
    if hasattr(args, 'lradj') and args.lradj == 'type1':
        lr_adjust = {epoch: args.learning_rate * (0.5 ** ((epoch - 1) // 1))}
    elif hasattr(args, 'lradj') and args.lradj == 'type2':
        lr_adjust = {
            2: 5e-5, 4: 1e-5, 6: 5e-6, 8: 1e-6,
            10: 5e-7, 15: 1e-7, 20: 5e-8
        }
    else:
        # 默认学习率调整策略
        lr_adjust = {epoch: args.learning_rate * (0.95 ** epoch)}
    
    if epoch in lr_adjust.keys():
        lr = lr_adjust[epoch]
        for param_group in optimizer.param_groups:
            param_group['lr'] = lr
        print('Updating learning rate to {}'.format(lr))


class StandardScaler:
    """标准化器"""
    def __init__(self, mean=None, std=None):
        self.mean = mean
        self.std = std
    
    def fit(self, data):
        self.mean = data.mean(0)
        self.std = data.std(0)
    
    def transform(self, data):
        return (data - self.mean) / self.std
    
    def inverse_transform(self, data):
        return (data * self.std) + self.mean


def visual(true, preds=None, name='./pic/test.pdf'):
    """可视化函数（简化版）"""
    # 这里可以添加matplotlib绘图代码
    # 为了避免依赖问题，暂时使用简化实现
    print(f"Visualization saved to {name}")
    return True


def adjustment(gt, pred):
    """调整预测结果"""
    anomaly_state = False
    for i in range(len(gt)):
        if gt[i] == 1 and pred[i] == 1 and not anomaly_state:
            anomaly_state = True
            for j in range(i, 0, -1):
                if gt[j] == 0:
                    break
                else:
                    if pred[j] == 0:
                        pred[j] = 1
            for j in range(i, len(gt)):
                if gt[j] == 0:
                    break
                else:
                    if pred[j] == 0:
                        pred[j] = 1
        elif gt[i] == 0:
            anomaly_state = False
        if anomaly_state:
            pred[i] = 1
    return gt, pred


def cal_accuracy(y_pred, y_true):
    """计算准确率"""
    return np.mean(y_pred == y_true)


def data_provider(args, flag):
    """数据提供器（简化版）"""
    # 这里应该返回相应的数据加载器
    # 为了避免循环导入，使用简化实现
    print(f"Data provider called with flag: {flag}")
    return None


def create_directory(path):
    """创建目录"""
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"Created directory: {path}")
    return path


def save_model(model, path):
    """保存模型"""
    create_directory(os.path.dirname(path))
    torch.save(model.state_dict(), path)
    print(f"Model saved to: {path}")


def load_model(model, path):
    """加载模型"""
    if os.path.exists(path):
        model.load_state_dict(torch.load(path, map_location='cpu'))
        print(f"Model loaded from: {path}")
        return True
    else:
        print(f"Model file not found: {path}")
        return False


def count_parameters(model):
    """计算模型参数数量"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    return total_params, trainable_params


def set_seed(seed=42):
    """设置随机种子"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    print(f"Random seed set to: {seed}")


def get_device():
    """获取设备"""
    if torch.cuda.is_available():
        device = torch.device('cuda')
        print(f"Using GPU: {torch.cuda.get_device_name()}")
    else:
        device = torch.device('cpu')
        print("Using CPU")
    return device


class AverageMeter:
    """平均值计算器"""
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0
    
    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count


def format_time(seconds):
    """格式化时间"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"


def print_model_summary(model, input_size=None):
    """打印模型摘要"""
    total_params, trainable_params = count_parameters(model)
    print("=" * 50)
    print("Model Summary")
    print("=" * 50)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    if input_size:
        print(f"Input size: {input_size}")
    print("=" * 50)


def validate_config(config):
    """验证配置"""
    required_attrs = ['seq_len', 'd_model', 'batch_size', 'learning_rate']
    missing_attrs = []
    
    for attr in required_attrs:
        if not hasattr(config, attr):
            missing_attrs.append(attr)
    
    if missing_attrs:
        print(f"Warning: Missing config attributes: {missing_attrs}")
        return False
    
    print("✓ Configuration validation passed")
    return True
