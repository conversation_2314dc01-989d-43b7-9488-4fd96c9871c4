import os
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler, LabelEncoder
import warnings

warnings.filterwarnings('ignore')


class FusionDataLoader(Dataset):
    """
    融合数据加载器，支持前驱信号检测和异常检测的统一数据接口
    """
    def __init__(self, root_path, win_size, step=1, flag="train", task_type="fusion"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.task_type = task_type
        self.scaler = StandardScaler()
        
        # 初始化编码器
        self.cw_encoder = LabelEncoder()
        self.rigsta_encoder = LabelEncoder()
        
        if task_type == "fusion":
            self._load_fusion_data(root_path)
        elif task_type == "anomaly_detection":
            self._load_anomaly_data(root_path)
        elif task_type == "earlysignaldet":
            self._load_earlysignal_data(root_path)
    
    def _load_fusion_data(self, root_path):
        """加载融合数据，同时支持两种任务的测试"""
        # 加载异常检测数据（CSV格式）
        self._load_anomaly_csv_data(os.path.join(root_path, "anomaly"))

        # 加载前驱信号检测数据
        self._load_earlysignal_files(os.path.join(root_path, "earlysignal"))

        print(f"Fusion data loaded:")
        print(f"  Anomaly train: {self.anomaly_train.shape}")
        print(f"  Anomaly test: {self.anomaly_test.shape}")
        print(f"  EarlySignal samples: {len(self.earlysignal_file_paths)}")

    def _load_anomaly_data(self, root_path):
        """仅加载异常检测数据（CSV格式）"""
        self._load_anomaly_csv_data(root_path)

        print(f"Anomaly detection data loaded:")
        print(f"  Train: {self.train.shape}")
        print(f"  Test: {self.test.shape}")

    def _load_anomaly_csv_data(self, anomaly_path):
        """加载CSV格式的异常检测数据"""
        # 加载训练数据
        train_files = [f for f in os.listdir(os.path.join(anomaly_path, "train")) if f.endswith('.csv')]
        train_data = []

        for file in train_files:
            try:
                data = pd.read_csv(os.path.join(anomaly_path, "train", file))
                # 移除时间列，只保留数值特征
                if 'date' in data.columns:
                    data = data.drop(['date'], axis=1)
                train_data.append(data.values)
            except Exception as e:
                print(f"Warning: Failed to load {file}: {e}")
                continue

        if train_data:
            self.anomaly_train = np.concatenate(train_data, axis=0)
        else:
            # 如果没有训练文件，创建空数组
            self.anomaly_train = np.array([]).reshape(0, 11)  # 假设11个特征

        # 标准化训练数据
        if len(self.anomaly_train) > 0:
            self.scaler.fit(self.anomaly_train)
            self.anomaly_train = self.scaler.transform(self.anomaly_train)

        # 加载测试数据
        test_file = os.path.join(anomaly_path, "test_data.csv")
        if os.path.exists(test_file):
            test_df = pd.read_csv(test_file)
            # 保存时间戳用于后续预警时间段分析
            if 'date' in test_df.columns:
                self.anomaly_test_timestamps = pd.to_datetime(test_df['date']).values
                test_df = test_df.drop(['date'], axis=1)
            else:
                self.anomaly_test_timestamps = np.arange(len(test_df))

            self.anomaly_test = test_df.values
            if len(self.anomaly_train) > 0:
                self.anomaly_test = self.scaler.transform(self.anomaly_test)
        else:
            self.anomaly_test = np.array([]).reshape(0, 11)
            self.anomaly_test_timestamps = np.array([])

        # 异常检测不需要测试标签，因为是无监督学习
        # 创建虚拟标签用于兼容性（全部设为0）
        self.anomaly_test_labels = np.zeros(len(self.anomaly_test))

        # 划分验证集
        if len(self.anomaly_train) > 0:
            data_len = len(self.anomaly_train)
            self.anomaly_val = self.anomaly_train[int(data_len * 0.8):]
            self.anomaly_train_timestamps = np.arange(self.anomaly_train.shape[0])
            self.anomaly_val_timestamps = self.anomaly_train_timestamps[int(data_len * 0.8):]
        else:
            self.anomaly_val = np.array([]).reshape(0, 11)
            self.anomaly_train_timestamps = np.array([])
            self.anomaly_val_timestamps = np.array([])

        # 为兼容性设置train, test等属性
        self.train = self.anomaly_train
        self.test = self.anomaly_test
        self.val = self.anomaly_val
        self.test_labels = self.anomaly_test_labels
        self.train_timestamps = self.anomaly_train_timestamps
        self.test_timestamps = self.anomaly_test_timestamps
    
    def _load_earlysignal_data(self, root_path):
        """仅加载前驱信号检测数据"""
        self._load_earlysignal_files(root_path)
        print(f"Early signal detection data loaded: {len(self.earlysignal_file_paths)} samples")
    
    def _load_earlysignal_files(self, base_folder):
        """加载前驱信号检测文件路径和标签"""
        self.earlysignal_file_paths = []
        self.earlysignal_labels = []
        
        for class_label, class_folder in enumerate(['正常数据', '前驱']):
            class_folder_path = os.path.join(base_folder, class_folder)
            if os.path.exists(class_folder_path):
                for file_name in os.listdir(class_folder_path):
                    self.earlysignal_file_paths.append(os.path.join(class_folder_path, file_name))
                    self.earlysignal_labels.append(class_label)
        
        # 获取最大序列长度
        self.max_seq_len = self._get_max_length(self.earlysignal_file_paths)
        
        # 预训练编码器
        self._fit_encoders()
    
    def _get_max_length(self, file_paths):
        """获取前驱信号数据的最大长度"""
        max_length = 0
        for file_path in file_paths:
            try:
                data = pd.read_csv(file_path, encoding='ANSI')
                if len(data) > max_length:
                    max_length = len(data)
            except:
                continue
        return min(max_length, 152)  # 限制最大长度为152
    
    def _fit_encoders(self):
        """预训练标签编码器"""
        cw_values = []
        rigsta_values = []
        
        for file_path in self.earlysignal_file_paths[:100]:  # 采样部分文件进行编码器训练
            try:
                data = pd.read_csv(file_path, encoding='ANSI')
                if 'CW' in data.columns:
                    cw_values.extend(data['CW'].fillna('unknown').astype(str).tolist())
                if 'RIGSTA' in data.columns:
                    rigsta_values.extend(data['RIGSTA'].fillna('unknown').astype(str).tolist())
            except:
                continue
        
        if cw_values:
            self.cw_encoder.fit(list(set(cw_values)))
        if rigsta_values:
            self.rigsta_encoder.fit(list(set(rigsta_values)))
    
    def _load_earlysignal_sample(self, file_path):
        """加载单个前驱信号样本"""
        try:
            data = pd.read_csv(file_path, encoding='ANSI')
            data = data.drop(['取样时间'], axis=1, errors='ignore')
            
            # 处理分类特征
            if 'CW' in data.columns:
                data['CW'] = data['CW'].fillna('unknown').astype(str)
                try:
                    data['CW'] = self.cw_encoder.transform(data['CW'])
                except:
                    data['CW'] = 0
            
            if 'RIGSTA' in data.columns:
                data['RIGSTA'] = data['RIGSTA'].fillna('unknown').astype(str)
                try:
                    data['RIGSTA'] = self.rigsta_encoder.transform(data['RIGSTA'])
                except:
                    data['RIGSTA'] = 0
            
            time_series = data.values.astype(np.float32)
            
            # 补零或截断到固定长度
            if len(time_series) < self.max_seq_len:
                padded_series = np.zeros((self.max_seq_len, time_series.shape[1]))
                padded_series[:len(time_series)] = time_series
            else:
                padded_series = time_series[:self.max_seq_len]
            
            return padded_series
        except Exception as e:
            # 返回零填充数据作为fallback
            return np.zeros((self.max_seq_len, 11))
    
    def __len__(self):
        if self.task_type == "fusion":
            if self.flag == "train":
                return (self.anomaly_train.shape[0] - self.win_size) // self.step + 1
            elif self.flag == 'val':
                return (self.anomaly_val.shape[0] - self.win_size) // self.step + 1
            elif self.flag == 'test':
                return (self.anomaly_test.shape[0] - self.win_size) // self.step + 1
            elif self.flag == 'earlysignal':
                return len(self.earlysignal_file_paths)
        elif self.task_type == "anomaly_detection":
            if self.flag == "train":
                return (self.train.shape[0] - self.win_size) // self.step + 1
            elif self.flag == 'val':
                return (self.val.shape[0] - self.win_size) // self.step + 1
            elif self.flag == 'test':
                return (self.test.shape[0] - self.win_size) // self.step + 1
        elif self.task_type == "earlysignaldet":
            return len(self.earlysignal_file_paths)
        
        return 0
    
    def __getitem__(self, index):
        if self.task_type == "fusion":
            if self.flag == 'earlysignal':
                # 返回前驱信号检测样本
                file_path = self.earlysignal_file_paths[index]
                label = self.earlysignal_labels[index]
                time_series = self._load_earlysignal_sample(file_path)
                return torch.FloatTensor(time_series), torch.LongTensor([label]), file_path
            else:
                # 返回异常检测样本
                index = index * self.step
                if self.flag == "train":
                    return (torch.FloatTensor(self.anomaly_train[index:index + self.win_size]),
                            torch.FloatTensor(self.anomaly_test_labels[0:self.win_size]),
                            self.anomaly_train_timestamps[index:index + self.win_size])
                elif self.flag == 'val':
                    return (torch.FloatTensor(self.anomaly_val[index:index + self.win_size]),
                            torch.FloatTensor(self.anomaly_test_labels[0:self.win_size]),
                            self.anomaly_val_timestamps[index:index + self.win_size])
                elif self.flag == 'test':
                    return (torch.FloatTensor(self.anomaly_test[index:index + self.win_size]),
                            torch.FloatTensor(self.anomaly_test_labels[index:index + self.win_size]),
                            self.anomaly_test_timestamps[index:index + self.win_size])
        
        elif self.task_type == "anomaly_detection":
            index = index * self.step
            if self.flag == "train":
                return (torch.FloatTensor(self.train[index:index + self.win_size]),
                        torch.FloatTensor(self.test_labels[0:self.win_size]),
                        self.train_timestamps[index:index + self.win_size])
            elif self.flag == 'val':
                return (torch.FloatTensor(self.val[index:index + self.win_size]),
                        torch.FloatTensor(self.test_labels[0:self.win_size]),
                        self.val_timestamps[index:index + self.win_size])
            elif self.flag == 'test':
                return (torch.FloatTensor(self.test[index:index + self.win_size]),
                        torch.FloatTensor(self.test_labels[index:index + self.win_size]),
                        self.test_timestamps[index:index + self.win_size])
        
        elif self.task_type == "earlysignaldet":
            file_path = self.earlysignal_file_paths[index]
            label = self.earlysignal_labels[index]
            time_series = self._load_earlysignal_sample(file_path)
            return torch.FloatTensor(time_series), torch.LongTensor([label]), file_path
        
        return None
