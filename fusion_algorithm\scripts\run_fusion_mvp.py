#!/usr/bin/env python3
"""
融合算法MVP运行脚本
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_fusion_training():
    """运行融合模型训练"""
    print("=" * 60)
    print("融合算法MVP - 训练模式")
    print("=" * 60)
    
    cmd = [
        "python", "run_fusion.py",
        "--task_name", "fusion",
        "--is_training", "1",
        "--root_path", "./dataset/",
        "--model_id", "fusion_mvp",
        "--model", "FusionModel",
        "--data", "fusion",
        "--seq_len", "96",
        "--d_model", "128",
        "--d_ff", "256",
        "--e_layers", "3",
        "--batch_size", "16",
        "--patch_len", "16",
        "--stride", "8",
        "--num_class", "2",
        "--train_epochs", "50",  # 减少训练轮数用于MVP测试
        "--patience", "10",
        "--learning_rate", "0.001",
        "--anomaly_ratio", "1.0",
        "--des", "MVP",
        "--itr", "1"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, check=True, capture_output=True, text=True)
        print("训练完成!")
        print("输出:", result.stdout)
        if result.stderr:
            print("警告:", result.stderr)
    except subprocess.CalledProcessError as e:
        print(f"训练失败: {e}")
        print("错误输出:", e.stderr)
        return False
    
    return True

def run_fusion_testing():
    """运行融合模型测试"""
    print("=" * 60)
    print("融合算法MVP - 测试模式")
    print("=" * 60)
    
    cmd = [
        "python", "run_fusion.py",
        "--task_name", "fusion",
        "--is_training", "0",
        "--root_path", "./dataset/",
        "--model_id", "fusion_mvp",
        "--model", "FusionModel",
        "--data", "fusion",
        "--seq_len", "96",
        "--d_model", "128",
        "--d_ff", "256",
        "--e_layers", "3",
        "--batch_size", "1",  # 测试时使用batch_size=1
        "--patch_len", "16",
        "--stride", "8",
        "--num_class", "2",
        "--anomaly_ratio", "1.0",
        "--des", "MVP",
        "--itr", "1"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, check=True, capture_output=True, text=True)
        print("测试完成!")
        print("输出:", result.stdout)
        if result.stderr:
            print("警告:", result.stderr)
    except subprocess.CalledProcessError as e:
        print(f"测试失败: {e}")
        print("错误输出:", e.stderr)
        return False
    
    return True

def setup_directories():
    """创建必要的目录结构"""
    directories = [
        project_root / "dataset" / "anomaly" / "train",
        project_root / "dataset" / "anomaly",
        project_root / "dataset" / "earlysignal" / "正常数据",
        project_root / "dataset" / "earlysignal" / "前驱",
        project_root / "checkpoints",
        project_root / "results"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {directory}")

def check_dependencies():
    """检查依赖项"""
    required_files = [
        project_root / "run_fusion.py",
        project_root / "exp" / "exp_fusion.py",
        project_root / "models" / "fusion_model.py",
        project_root / "data_provider" / "fusion_data_loader.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not file_path.exists():
            missing_files.append(str(file_path))
    
    if missing_files:
        print("缺少以下文件:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    
    print("所有必需文件都存在")
    return True

def print_data_structure_guide():
    """打印数据结构指南"""
    print("\n" + "=" * 60)
    print("数据结构指南")
    print("=" * 60)
    print("""
请按以下结构组织您的数据:

dataset/
├── anomaly/
│   ├── train/
│   │   ├── train_file1.npy
│   │   ├── train_file2.npy
│   │   └── ...
│   ├── clean_宁209H8-3钻井日志_test.npy
│   └── 宁209H8-3钻井日志_test_label.npy
└── earlysignal/
    ├── 正常数据/
    │   ├── normal_sample1.csv
    │   ├── normal_sample2.csv
    │   └── ...
    └── 前驱/
        ├── precursor_sample1.csv
        ├── precursor_sample2.csv
        └── ...

注意:
1. 异常检测数据应为.npy格式的数值数组
2. 前驱信号数据应为.csv格式，包含时间序列特征
3. CSV文件应包含'取样时间'列（将被自动删除）
4. 确保数据路径与配置中的root_path匹配
    """)

def main():
    """主函数"""
    print("融合算法MVP启动器")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("请确保所有必需文件都存在后再运行")
        return
    
    # 创建目录
    setup_directories()
    
    # 打印数据结构指南
    print_data_structure_guide()
    
    # 询问用户操作
    while True:
        print("\n请选择操作:")
        print("1. 训练融合模型")
        print("2. 测试融合模型")
        print("3. 训练+测试")
        print("4. 退出")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == '1':
            run_fusion_training()
        elif choice == '2':
            run_fusion_testing()
        elif choice == '3':
            if run_fusion_training():
                run_fusion_testing()
        elif choice == '4':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
