# 融合算法测试验证框架

本测试框架专门为融合算法的验证和测试而设计，包含多个层次的测试模块，确保算法的正确性、稳定性和性能。

## 📁 测试文件结构

```
fusion_algorithm/tests/
├── README.md                    # 本说明文件
├── test_fusion_algorithm.py     # 核心融合算法测试
├── test_data_validation.py      # 数据验证测试
├── test_algorithm_logic.py      # 算法逻辑验证测试
├── quick_validation.py          # 快速验证脚本
├── run_comprehensive_tests.py   # 综合测试运行器
└── reports/                     # 测试报告目录
    ├── test_report_*.json       # 详细JSON报告
    └── test_summary_*.txt       # 文本摘要报告
```

## 🧪 测试模块说明

### 1. 核心融合算法测试 (`test_fusion_algorithm.py`)
- **TestDataLoader**: 验证数据加载器的正确性
- **TestFusionModel**: 验证融合模型的架构和前向传播
- **TestWarningFusion**: 验证预警融合逻辑
- **TestFusionAlgorithm**: 基础测试类，提供测试数据生成

**主要验证点:**
- 数据加载器初始化和数据获取
- 模型前向传播的输出维度和数值稳定性
- 损失函数的计算正确性
- 预警时间段的提取和合并逻辑

### 2. 数据验证测试 (`test_data_validation.py`)
验证真实数据的格式、完整性和质量。

**验证内容:**
- 数据文件存在性检查
- CSV文件格式验证
- 文件名格式规范性
- 数据质量评估（缺失值、异常值）
- 时间序列连续性检查

**输出:** 生成 `data_validation_report.txt` 详细报告

### 3. 算法逻辑验证测试 (`test_algorithm_logic.py`)
专注于验证算法的核心逻辑正确性。

**测试内容:**
- **TestAlgorithmLogic**: 核心逻辑测试
  - 模型输出一致性
  - 异常检测逻辑验证
  - 前驱信号分类逻辑验证
  - 损失函数行为测试
  - 预警融合逻辑测试
  - 边界条件处理
  - 梯度流动验证

- **TestAlgorithmAccuracy**: 准确性测试
  - 合成数据性能测试
  - 已知模式识别能力

### 4. 快速验证脚本 (`quick_validation.py`)
提供快速的端到端功能验证。

**验证流程:**
1. 数据加载验证
2. 模型创建验证
3. 前向传播验证
4. 训练步骤验证
5. 推理过程验证
6. 预警融合验证

**特点:**
- 快速执行（通常1-2分钟）
- 直观的成功/失败反馈
- 适合开发过程中的快速检查

### 5. 综合测试运行器 (`run_comprehensive_tests.py`)
统一运行所有测试模块并生成详细报告。

**功能:**
- 自动运行所有测试套件
- 收集详细的测试结果
- 生成JSON和文本格式的报告
- 提供测试建议和改进方向

## 🚀 使用方法

### 快速开始

1. **快速验证** (推荐首次使用)
```bash
cd fusion_algorithm/tests
python quick_validation.py
```

2. **运行特定测试模块**
```bash
# 数据验证
python test_data_validation.py

# 算法逻辑测试
python test_algorithm_logic.py

# 核心功能测试
python test_fusion_algorithm.py
```

3. **运行完整测试套件**
```bash
python run_comprehensive_tests.py
```

### 测试环境要求

**Python依赖:**
```
torch >= 1.9.0
pandas >= 1.3.0
numpy >= 1.21.0
unittest (标准库)
```

**数据要求:**
- 异常检测数据: `异常检测/Time-Series-Library-main-loss/Time-Series-Library-main/dataset/`
- 前驱信号数据: `前驱信号检测/dataset/`

## 📊 测试报告解读

### 快速验证报告
```
✓ Data Loading: SUCCESS        # 数据加载成功
✓ Model Creation: SUCCESS      # 模型创建成功
✓ Forward Pass: SUCCESS        # 前向传播正常
✓ Training Step: SUCCESS       # 训练步骤正常
✓ Inference: SUCCESS           # 推理过程正常
✓ Warning Fusion: SUCCESS      # 预警融合正常

成功率: 6/6 (100.0%)
```

### 综合测试报告
测试报告保存在 `reports/` 目录下：

- **JSON报告** (`test_report_*.json`): 包含详细的测试结果、错误信息和性能数据
- **文本摘要** (`test_summary_*.txt`): 人类可读的测试摘要和建议

## 🔧 故障排除

### 常见问题

1. **导入模块失败**
   - 确保项目路径正确
   - 检查所有依赖模块是否已实现

2. **数据路径不存在**
   - 检查数据文件是否在正确位置
   - 验证文件路径配置

3. **CUDA相关错误**
   - 测试会自动检测并使用CPU/GPU
   - 如有GPU问题，会自动回退到CPU

4. **内存不足**
   - 减少批次大小 (`batch_size`)
   - 减少序列长度 (`seq_len`)

### 调试建议

1. **逐步测试**: 先运行 `quick_validation.py`，确定基本功能正常
2. **查看详细日志**: 使用 `python -v` 运行测试获取详细输出
3. **检查数据**: 运行 `test_data_validation.py` 确保数据质量
4. **单独测试**: 针对失败的模块单独运行测试

## 📈 性能基准

### 预期性能指标

**模型规模:**
- 参数数量: ~100K - 1M
- 内存使用: < 1GB
- 训练速度: > 10 samples/sec

**准确性目标:**
- 异常检测: 重构误差区分度 > 2x
- 前驱信号分类: 概率输出合理性
- 预警融合: 时间段合并正确性

## 🔄 持续集成

建议在以下情况运行测试：

1. **代码修改后**: 运行 `quick_validation.py`
2. **数据更新后**: 运行 `test_data_validation.py`
3. **发布前**: 运行 `run_comprehensive_tests.py`
4. **性能优化后**: 对比测试报告中的性能数据

## 📝 扩展测试

### 添加新测试

1. 在相应的测试文件中添加新的测试方法
2. 方法名以 `test_` 开头
3. 使用 `self.assert*` 方法进行断言
4. 添加适当的文档字符串

### 自定义验证

可以基于现有框架创建特定场景的测试：

```python
class TestCustomScenario(TestFusionAlgorithm):
    def test_specific_case(self):
        # 自定义测试逻辑
        pass
```

## 📞 支持

如果遇到测试相关问题：

1. 查看测试报告中的详细错误信息
2. 检查数据和环境配置
3. 参考本文档的故障排除部分
4. 查看代码注释和文档字符串

---

**注意**: 本测试框架专门针对融合算法设计，确保在运行测试前已正确配置数据路径和环境依赖。
