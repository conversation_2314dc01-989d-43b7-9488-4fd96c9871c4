#!/usr/bin/env python3
"""
评估指标模块
提供各种评估指标的计算函数
"""

import numpy as np
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import roc_auc_score, roc_curve, precision_recall_curve
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')


def RSE(pred, true):
    """相对平方误差 (Relative Squared Error)"""
    return np.sqrt(np.sum((true - pred) ** 2)) / np.sqrt(np.sum((true - true.mean()) ** 2))


def CORR(pred, true):
    """相关系数"""
    u = ((true - true.mean(0)) * (pred - pred.mean(0))).sum(0)
    d = np.sqrt(((true - true.mean(0)) ** 2 * (pred - pred.mean(0)) ** 2).sum(0))
    d += 1e-12
    return 0.01 * (u / d).mean(-1)


def MAE(pred, true):
    """平均绝对误差 (Mean Absolute Error)"""
    return np.mean(np.abs(pred - true))


def MSE(pred, true):
    """均方误差 (Mean Squared Error)"""
    return np.mean((pred - true) ** 2)


def RMSE(pred, true):
    """均方根误差 (Root Mean Squared Error)"""
    return np.sqrt(MSE(pred, true))


def MAPE(pred, true):
    """平均绝对百分比误差 (Mean Absolute Percentage Error)"""
    return np.mean(np.abs((pred - true) / true)) * 100


def MSPE(pred, true):
    """均方百分比误差 (Mean Squared Percentage Error)"""
    return np.mean(np.square((pred - true) / true)) * 100


def metric(pred, true):
    """综合评估指标"""
    mae = MAE(pred, true)
    mse = MSE(pred, true)
    rmse = RMSE(pred, true)
    mape = MAPE(pred, true)
    mspe = MSPE(pred, true)
    rse = RSE(pred, true)
    corr = CORR(pred, true)
    
    return mae, mse, rmse, mape, mspe, rse, corr


def anomaly_detection_metrics(y_true, y_pred, y_scores=None):
    """异常检测评估指标"""
    # 基本分类指标
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, zero_division=0)
    recall = recall_score(y_true, y_pred, zero_division=0)
    f1 = f1_score(y_true, y_pred, zero_division=0)
    
    metrics_dict = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }
    
    # 如果提供了分数，计算AUC
    if y_scores is not None:
        try:
            auc = roc_auc_score(y_true, y_scores)
            metrics_dict['auc'] = auc
        except ValueError:
            metrics_dict['auc'] = 0.0
    
    return metrics_dict


def classification_metrics(y_true, y_pred, y_proba=None):
    """分类任务评估指标"""
    # 基本指标
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, average='weighted', zero_division=0)
    recall = recall_score(y_true, y_pred, average='weighted', zero_division=0)
    f1 = f1_score(y_true, y_pred, average='weighted', zero_division=0)
    
    metrics_dict = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }
    
    # 如果是二分类且提供了概率，计算AUC
    if y_proba is not None and len(np.unique(y_true)) == 2:
        try:
            if y_proba.ndim > 1:
                y_proba = y_proba[:, 1]  # 取正类概率
            auc = roc_auc_score(y_true, y_proba)
            metrics_dict['auc'] = auc
        except ValueError:
            metrics_dict['auc'] = 0.0
    
    return metrics_dict


def regression_metrics(y_true, y_pred):
    """回归任务评估指标"""
    mae = MAE(y_pred, y_true)
    mse = MSE(y_pred, y_true)
    rmse = RMSE(y_pred, y_true)
    
    # 避免除零错误
    try:
        mape = MAPE(y_pred, y_true)
        mspe = MSPE(y_pred, y_true)
    except (ZeroDivisionError, RuntimeWarning):
        mape = float('inf')
        mspe = float('inf')
    
    rse = RSE(y_pred, y_true)
    corr = CORR(y_pred, y_true)
    
    return {
        'mae': mae,
        'mse': mse,
        'rmse': rmse,
        'mape': mape,
        'mspe': mspe,
        'rse': rse,
        'corr': corr
    }


def calculate_threshold_metrics(y_true, y_scores, thresholds=None):
    """计算不同阈值下的指标"""
    if thresholds is None:
        thresholds = np.linspace(0.1, 0.9, 9)
    
    results = []
    for threshold in thresholds:
        y_pred = (y_scores >= threshold).astype(int)
        metrics = anomaly_detection_metrics(y_true, y_pred, y_scores)
        metrics['threshold'] = threshold
        results.append(metrics)
    
    return results


def find_best_threshold(y_true, y_scores, metric='f1_score'):
    """找到最佳阈值"""
    thresholds = np.linspace(0.01, 0.99, 99)
    best_threshold = 0.5
    best_score = 0
    
    for threshold in thresholds:
        y_pred = (y_scores >= threshold).astype(int)
        metrics = anomaly_detection_metrics(y_true, y_pred, y_scores)
        
        if metrics[metric] > best_score:
            best_score = metrics[metric]
            best_threshold = threshold
    
    return best_threshold, best_score


def print_metrics(metrics_dict, title="Evaluation Metrics"):
    """打印评估指标"""
    print(f"\n{title}")
    print("=" * len(title))
    
    for key, value in metrics_dict.items():
        if isinstance(value, float):
            if abs(value) < 1e-3 or abs(value) > 1e3:
                print(f"{key:12}: {value:.6e}")
            else:
                print(f"{key:12}: {value:.6f}")
        else:
            print(f"{key:12}: {value}")


def confusion_matrix_metrics(y_true, y_pred):
    """混淆矩阵相关指标"""
    from sklearn.metrics import confusion_matrix
    
    cm = confusion_matrix(y_true, y_pred)
    
    if cm.shape == (2, 2):
        tn, fp, fn, tp = cm.ravel()
        
        # 计算各种指标
        sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0  # 召回率/敏感性
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0  # 特异性
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0    # 精确率
        npv = tn / (tn + fn) if (tn + fn) > 0 else 0          # 负预测值
        
        return {
            'confusion_matrix': cm,
            'true_positive': tp,
            'true_negative': tn,
            'false_positive': fp,
            'false_negative': fn,
            'sensitivity': sensitivity,
            'specificity': specificity,
            'precision': precision,
            'negative_predictive_value': npv
        }
    else:
        return {'confusion_matrix': cm}


def time_series_metrics(y_true, y_pred, seasonal_periods=None):
    """时间序列特定指标"""
    basic_metrics = regression_metrics(y_true, y_pred)
    
    # 趋势准确性
    if len(y_true) > 1:
        true_diff = np.diff(y_true, axis=0)
        pred_diff = np.diff(y_pred, axis=0)
        
        # 方向准确性
        direction_accuracy = np.mean(np.sign(true_diff) == np.sign(pred_diff))
        basic_metrics['direction_accuracy'] = direction_accuracy
    
    # 如果提供了季节性周期，计算季节性指标
    if seasonal_periods:
        # 这里可以添加季节性分解和评估
        pass
    
    return basic_metrics


def early_warning_metrics(y_true, y_pred, warning_horizon=10):
    """早期预警指标"""
    # 计算预警提前量
    true_anomalies = np.where(y_true == 1)[0]
    pred_warnings = np.where(y_pred == 1)[0]
    
    if len(true_anomalies) == 0 or len(pred_warnings) == 0:
        return {
            'early_warning_rate': 0.0,
            'false_alarm_rate': 1.0 if len(pred_warnings) > 0 else 0.0,
            'average_lead_time': 0.0
        }
    
    # 计算每个真实异常的最早预警时间
    lead_times = []
    detected_anomalies = 0
    
    for anomaly_idx in true_anomalies:
        # 查找在异常发生前warning_horizon时间窗口内的预警
        early_warnings = pred_warnings[
            (pred_warnings < anomaly_idx) & 
            (pred_warnings >= anomaly_idx - warning_horizon)
        ]
        
        if len(early_warnings) > 0:
            lead_time = anomaly_idx - early_warnings[-1]  # 最近的预警
            lead_times.append(lead_time)
            detected_anomalies += 1
    
    early_warning_rate = detected_anomalies / len(true_anomalies)
    average_lead_time = np.mean(lead_times) if lead_times else 0.0
    
    # 计算误报率
    total_predictions = len(pred_warnings)
    false_alarms = total_predictions - detected_anomalies
    false_alarm_rate = false_alarms / total_predictions if total_predictions > 0 else 0.0
    
    return {
        'early_warning_rate': early_warning_rate,
        'false_alarm_rate': false_alarm_rate,
        'average_lead_time': average_lead_time,
        'detected_anomalies': detected_anomalies,
        'total_anomalies': len(true_anomalies)
    }
