import pandas as pd
import os

# 文件夹路径（请替换为你自己的文件夹路径）
folder_path = "D:\卡钻\最新数据/2024.6.1430口井卡钻预警测试数据\新建文件夹"  # 例如 "D:/your_data_folder"

# 定义标准列顺序
columns_order = [
    'date', 'DEP', 'BITDEP', 'HOKHEI', 'DRITIME',
    'WOB', 'HKLD', 'RPM', 'TOR', 'FLOWIN',
    'FLOWOUT', 'SPP', 'CSIP'
]

# 遍历文件夹下所有CSV文件
for file_name in os.listdir(folder_path):
    if file_name.endswith(".csv"):
        file_path = os.path.join(folder_path, file_name)
        try:
            # 读取CSV文件
            df = pd.read_csv(file_path)

            # 验证列完整性
            missing_cols = [col for col in columns_order if col not in df.columns]
            if missing_cols:
                raise KeyError(f"缺失必要列: {missing_cols}")

            # 重新排序列
            df = df[columns_order]

            # 覆盖原文件
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            print(f"文件 {file_name} 已成功更新并覆盖")

        except Exception as e:
            print(f"处理失败: {file_name}，原因: {str(e)}")
            print("原文件保持不变")
