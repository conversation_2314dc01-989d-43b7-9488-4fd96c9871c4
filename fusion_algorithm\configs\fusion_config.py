"""
融合算法配置文件
"""

class FusionConfig:
    """融合算法配置类"""
    
    def __init__(self):
        # 基础配置
        self.task_name = 'fusion'
        self.model_id = 'fusion_mvp'
        self.model = 'FusionModel'
        
        # 数据配置
        self.root_path = './dataset/'
        self.seq_len = 96
        self.win_size = 96
        self.features = 'M'
        self.enc_in = 11
        self.dec_in = 11
        self.c_out = 11
        
        # 模型配置
        self.d_model = 128
        self.n_heads = 8
        self.e_layers = 3
        self.d_layers = 1
        self.d_ff = 256
        self.factor = 1
        self.dropout = 0.1
        self.activation = 'gelu'
        self.output_attention = False
        
        # Patch配置
        self.patch_len = 16
        self.stride = 8
        
        # 分类配置
        self.num_class = 2
        
        # 训练配置
        self.batch_size = 16
        self.learning_rate = 0.001
        self.train_epochs = 100
        self.patience = 10
        self.itr = 1
        
        # 异常检测配置
        self.anomaly_ratio = 1.0
        
        # GPU配置
        self.use_gpu = True
        self.gpu = 0
        self.use_multi_gpu = False
        self.devices = '0'
        
        # 其他配置
        self.checkpoints = './checkpoints/'
        self.num_workers = 0
        self.des = 'MVP'


# 预定义配置
ANOMALY_DETECTION_CONFIG = {
    'task_name': 'anomaly_detection',
    'model_id': 'anomaly_mvp',
    'seq_len': 96,
    'd_model': 16,
    'd_ff': 32,
    'e_layers': 2,
    'batch_size': 32,
    'patch_len': 16,
    'stride': 8
}

EARLYSIGNAL_DETECTION_CONFIG = {
    'task_name': 'earlysignaldet',
    'model_id': 'earlysignal_mvp',
    'seq_len': 152,
    'd_model': 128,
    'd_ff': 256,
    'e_layers': 3,
    'batch_size': 16,
    'patch_len': 8,
    'stride': 2,
    'num_class': 2
}

FUSION_CONFIG = {
    'task_name': 'fusion',
    'model_id': 'fusion_mvp',
    'seq_len': 96,
    'd_model': 128,
    'd_ff': 256,
    'e_layers': 3,
    'batch_size': 16,
    'patch_len': 16,
    'stride': 8,
    'num_class': 2,
    'anomaly_ratio': 1.0
}
