import torch
from torch import nn
import torch.nn.functional as F
from layers.Transformer_EncDec import Encoder, EncoderLayer
from layers.SelfAttention_Family import FullAttention, AttentionLayer
from layers.Embed import PatchEmbedding


class Model(nn.Module):
    """
    增强型PatchTST模型，融合了专家特征
    """
    def __init__(self, configs):
        super().__init__()
        self.task_name = configs.task_name
        self.seq_len = configs.seq_len
        self.pred_len = configs.pred_len
        self.enc_in = configs.enc_in if hasattr(configs, 'enc_in') else 7
        
        # 原始PatchTST的补丁嵌入
        self.patch_len = getattr(configs, 'patch_len', 16)
        self.stride = getattr(configs, 'stride', 8)
        
        self.patch_embedding = PatchEmbedding(
            configs.d_model, 
            patch_len=self.patch_len,
            stride=self.stride,
            padding=getattr(configs, 'padding', 1),
            dropout=configs.dropout
        )
        
        # Encoder
        self.encoder = Encoder(
            [
                EncoderLayer(
                    AttentionLayer(
                        FullAttention(False, configs.factor, attention_dropout=configs.dropout,
                                     output_attention=False), configs.d_model, configs.n_heads),
                    configs.d_model,
                    configs.d_ff,
                    dropout=configs.dropout,
                    activation=configs.activation
                ) for l in range(configs.e_layers)
            ],
            norm_layer=torch.nn.LayerNorm(configs.d_model)
        )
        
        # 专家特征处理
        self.expert_feature_dim = 7  # 6个特征 + 1个专家得分
        self.expert_projection = nn.Sequential(
            nn.Linear(self.expert_feature_dim, configs.d_model // 2),
            nn.ReLU(),
            nn.Dropout(configs.dropout),
            nn.Linear(configs.d_model // 2, configs.d_model // 2)
        )
        
        # 平坦化层
        self.flatten = nn.Flatten(start_dim=-2)
        self.dropout = nn.Dropout(configs.dropout)
        
        # 计算大致的时间特征维度（用于初始化，可能在forward中被替换）
        patch_num = (self.seq_len - self.patch_len) // self.stride + 2
        time_feature_dim = configs.d_model * patch_num * self.enc_in
        expert_feature_dim = configs.d_model // 2
        
        # 初始化融合层（可能在forward中被替换）
        self.fusion_layer = nn.Linear(time_feature_dim + expert_feature_dim, configs.d_model)
        
        # 输出层
        self.output_layer = nn.Linear(configs.d_model, configs.num_class)
        
        # 是否已经替换过融合层
        self.fusion_layer_replaced = False
        
        # 专家规则权重
        self.expert_weight = getattr(configs, 'expert_weight', 0.3)
        self.expert_threshold = getattr(configs, 'expert_threshold', 4)
    
    def forward(self, x_enc, x_mark_enc, x_expert, non_temp=None):
        """
        前向传播函数
        
        参数：
        x_enc: 时间序列输入 [bs, seq_len, nvars]
        x_mark_enc: 时间特征编码（可选）
        x_expert: 专家特征 [bs, seq_len, 7]
        non_temp: 非时间序列特征（可选）
        """
        # 获取批次大小
        batch_size = x_enc.shape[0]
        
        # 标准化
        means = x_enc.mean(1, keepdim=True).detach()
        x_enc = x_enc - means
        stdev = torch.sqrt(torch.var(x_enc, dim=1, keepdim=True, unbiased=False) + 1e-5)
        x_enc /= stdev
        
        # 处理时间序列特征
        x_enc = x_enc.permute(0, 2, 1)  # [bs, nvars, seq_len]
        enc_out, n_vars = self.patch_embedding(x_enc)
        
        enc_out, _ = self.encoder(enc_out)
        
        enc_out = torch.reshape(enc_out, (-1, n_vars, enc_out.shape[-2], enc_out.shape[-1]))
        
        enc_out = enc_out.permute(0, 1, 3, 2)  # [bs, nvars, d_model, patch_num]
        
        # 提取时间序列特征
        time_features = self.flatten(enc_out)
        
        time_features = self.dropout(time_features)
        time_features = time_features.reshape(time_features.shape[0], -1)  # [bs, nvars*d_model*patch_num]
        
        # 处理专家特征
        # 获取最后一个时间点的专家特征
        if x_expert is not None:
            last_expert_features = x_expert[:, -1, :]  # [bs, 7]
            
            expert_embedding = self.expert_projection(last_expert_features)
            
            # 融合特征
            combined_features = torch.cat([time_features, expert_embedding], dim=1)
            
            # 检查融合层的输入维度是否匹配
            if not self.fusion_layer_replaced and combined_features.shape[1] != self.fusion_layer.in_features:
                # 如果不匹配，则替换融合层
                time_feature_dim = time_features.shape[1]
                expert_feature_dim = expert_embedding.shape[1]
                total_input_dim = time_feature_dim + expert_feature_dim
                
                # 创建新的融合层并复制设备
                new_fusion_layer = nn.Linear(total_input_dim, self.output_layer.in_features).to(self.fusion_layer.weight.device)
                
                # 替换旧的融合层
                self.fusion_layer = new_fusion_layer
                self.fusion_layer_replaced = True
                print(f"已替换融合层，新输入维度: {total_input_dim}")
            
            # 应用融合层
            fused_features = self.fusion_layer(combined_features)
            
            fused_features = F.relu(fused_features)
            fused_features = F.dropout(fused_features, p=0.1, training=self.training)
        else:
            # 如果没有专家特征，就只使用时间序列特征
            # 检查融合层的输入维度是否匹配
            if not self.fusion_layer_replaced and time_features.shape[1] != self.fusion_layer.in_features:
                # 如果不匹配，则替换融合层
                time_feature_dim = time_features.shape[1]
                
                # 创建新的融合层并复制设备
                new_fusion_layer = nn.Linear(time_feature_dim, self.output_layer.in_features).to(self.fusion_layer.weight.device)
                
                # 替换旧的融合层
                self.fusion_layer = new_fusion_layer
                self.fusion_layer_replaced = True
                print(f"已替换融合层（无专家特征），新输入维度: {time_feature_dim}")
                
            fused_features = self.fusion_layer(time_features)
            fused_features = F.relu(fused_features)
            fused_features = F.dropout(fused_features, p=0.1, training=self.training)
        
        # 输出层
        output = self.output_layer(fused_features)
        
        return output
    
    def get_fusion_prediction(self, model_output, expert_score):
        """
        融合模型输出和专家规则评分得到最终预测
        
        参数:
        model_output: 模型输出的logits
        expert_score: 专家规则评分
        
        返回:
        final_prob: 融合后的概率
        prediction: 预测类别
        """
        # 计算原始概率
        prob = F.softmax(model_output, dim=1)
        risk_score = prob[:, 1]  # 取第1类（卡钻）的概率
        
        # 应用专家规则融合
        if expert_score >= self.expert_threshold:
            # 当专家评分高时，增强风险评分
            final_risk = risk_score * (1 - self.expert_weight) + self.expert_weight
        else:
            # 专家评分低时，保持原始风险评分
            final_risk = risk_score
        
        # 返回融合后的风险评分和预测
        return final_risk 