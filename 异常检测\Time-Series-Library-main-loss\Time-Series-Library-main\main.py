import subprocess

command = [
"python", "-u", "run.py",
"--task_name", "anomaly_detection",
"--is_training", "0",
"--root_path", "./dataset/test/",
"--data_path", "ETTh1.csv",
"--model_id", "ETTh1_17_11",
"--model", "Autoformer",
"--data", "ETTh1",
"--features", "M",
"--seq_len", "96",
"--label_len", "48",
"--pred_len", "96",
"--e_layers", "2",
"--d_layers", "1",
"--factor", "3",
"--enc_in", "12",
"--dec_in", "12",
"--c_out", "12",
"--d_model", "16",
"--d_ff", "32",
"--des", "Exp",
"--itr", "1",
"--top_k", "5",
]

subprocess.run(command)