# import pandas as pd
#
# # 输入CSV文件和TXT文件的路径
# csv_file_path = '2井.csv'
# txt_file_path = 'anomalies.txt'
#
# # 读取CSV文件
# df = pd.read_csv(csv_file_path)
#
# # 读取TXT文件并获取所有索引
# with open(txt_file_path, 'r') as file:
#     stuck_indexes = [int(line.strip()) for line in file]
#
# # 初始化'sfkz'列为0
# df['sfkz'] = 0
#
# # 遍历每个索引，将对应的行的'sfkz'值设置为1
# for index in stuck_indexes:
#     if 0 <= index - 1 < len(df):  # 确保索引在数据框的范围内
#         df.at[index - 1, 'sfkz'] = 1
#
# # 保存更新后的数据框回CSV文件
# df.to_csv(csv_file_path, index=False)
#
# print(f"已更新CSV文件并添加'sfkz'列。")
import pandas as pd
import chardet
import os

# 输入CSV文件和TXT文件的路径
csv_file_path = os.path.join('原始', '宁216H8-4.csv')
txt_file_path = 'anomalies.txt'

# 检测CSV文件的编码格式
with open(csv_file_path, 'rb') as f:
    result = chardet.detect(f.read())
encoding = result['encoding']

# 读取CSV文件
df = pd.read_csv(csv_file_path, encoding=encoding)

# 读取TXT文件并获取所有索引
with open(txt_file_path, 'r', encoding='utf-8') as file:
    stuck_indexes = [int(line.strip()) for line in file]

# 初始化'sfkz'列为0
df['sfkz'] = 0

# 遍历每个索引，将对应的行的'sfkz'值设置为1
for index in stuck_indexes:
    if 0 <= index - 1 < len(df):  # 确保索引在数据框的范围内
        df.at[index - 1, 'sfkz'] = 1

# 保存更新后的数据框回CSV文件
df.to_csv(csv_file_path, index=False, encoding=encoding)

print(f"已更新CSV文件并添加'sfkz'列。")
